# C/C++ build system timings
generate_cxx_metadata
  [gap of 115ms]
  create-invalidation-state 68ms
  generate-prefab-packages
    [gap of 34ms]
    exec-prefab 5096ms
    [gap of 98ms]
  generate-prefab-packages completed in 5228ms
  execute-generate-process
    [gap of 12ms]
    exec-configure 19010ms
    [gap of 1886ms]
  execute-generate-process completed in 20908ms
  [gap of 133ms]
  remove-unexpected-so-files 44ms
  [gap of 653ms]
  write-metadata-json-to-file 153ms
  [gap of 18ms]
generate_cxx_metadata completed in 27357ms

