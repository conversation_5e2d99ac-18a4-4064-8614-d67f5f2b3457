@echo off
"C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HH:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=26" ^
  "-DANDROID_PLATFORM=android-26" ^
  "-DANDROID_ABI=x86" ^
  "-DCMAKE_ANDROID_ARCH_ABI=x86" ^
  "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393" ^
  "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\2v5m6h18\\obj\\x86" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\2v5m6h18\\obj\\x86" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-DCMAKE_FIND_ROOT_PATH=H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\prefab\\x86\\prefab" ^
  "-BH:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\x86" ^
  -GNinja ^
  "-DANDROID_STL=c++_shared" ^
  "-DREACT_NATIVE_MINOR_VERSION=73" ^
  "-DANDROID_TOOLCHAIN=clang" ^
  "-DREACT_NATIVE_DIR=H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native" ^
  "-DJS_RUNTIME=hermes" ^
  "-DJS_RUNTIME_DIR=H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native\\sdks\\hermes" ^
  "-DIS_NEW_ARCHITECTURE_ENABLED=false" ^
  "-DIS_REANIMATED_EXAMPLE_APP=false" ^
  "-DREANIMATED_VERSION=3.13.0" ^
  "-DHERMES_ENABLE_DEBUGGER=1"
