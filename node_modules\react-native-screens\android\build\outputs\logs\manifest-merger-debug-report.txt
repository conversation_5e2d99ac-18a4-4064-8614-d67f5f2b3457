-- Merging decision tree log ---
manifest
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-screens\android\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-screens\android\src\main\AndroidManifest.xml:2:1-5:12
	package
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-screens\android\src\main\AndroidManifest.xml:3:11-44
		INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-screens\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-screens\android\src\main\AndroidManifest.xml:2:11-69
uses-sdk
INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-screens\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-screens\android\src\main\AndroidManifest.xml
INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-screens\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-screens\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-screens\android\src\main\AndroidManifest.xml
