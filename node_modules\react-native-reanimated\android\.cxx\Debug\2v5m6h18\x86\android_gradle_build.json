{"buildFiles": ["H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\hermes-engine\\hermes-engineConfig.cmake", "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\hermes-engine\\hermes-engineConfigVersion.cmake", "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"reanimated::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86", "artifactName": "reanimated", "output": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\2v5m6h18\\obj\\x86\\libreanimated.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\24bc47c525967d5c2a6bcc66e53ba8c8\\transformed\\jetified-react-android-0.73.8-debug\\prefab\\modules\\folly_runtime\\libs\\android.x86\\libfolly_runtime.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\24bc47c525967d5c2a6bcc66e53ba8c8\\transformed\\jetified-react-android-0.73.8-debug\\prefab\\modules\\glog\\libs\\android.x86\\libglog.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\24bc47c525967d5c2a6bcc66e53ba8c8\\transformed\\jetified-react-android-0.73.8-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\24bc47c525967d5c2a6bcc66e53ba8c8\\transformed\\jetified-react-android-0.73.8-debug\\prefab\\modules\\reactnativejni\\libs\\android.x86\\libreactnativejni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\96f49ffbed5d5e97cf1c70574efe45b1\\transformed\\jetified-fbjni-0.5.1\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\34ed84f9f7d41443bb4242a6896db335\\transformed\\jetified-hermes-android-0.73.8-debug\\prefab\\modules\\libhermes\\libs\\android.x86\\libhermes.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\24bc47c525967d5c2a6bcc66e53ba8c8\\transformed\\jetified-react-android-0.73.8-debug\\prefab\\modules\\hermes_executor\\libs\\android.x86\\libhermes_executor.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}