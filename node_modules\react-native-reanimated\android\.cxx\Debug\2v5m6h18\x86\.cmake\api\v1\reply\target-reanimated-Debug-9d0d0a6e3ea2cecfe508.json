{"artifacts": [{"path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/2v5m6h18/obj/x86/libreanimated.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_compile_options", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 50, "parent": 0}, {"command": 1, "file": 0, "line": 93, "parent": 0}, {"command": 1, "file": 0, "line": 99, "parent": 0}, {"command": 1, "file": 0, "line": 112, "parent": 0}, {"command": 1, "file": 0, "line": 118, "parent": 0}, {"command": 2, "file": 0, "line": 14, "parent": 0}, {"command": 3, "file": 0, "line": 61, "parent": 0}, {"command": 3, "file": 0, "line": 76, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC"}, {"backtrace": 6, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 6, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 6, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 6, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 6, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 6, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 6, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 6, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}, {"fragment": "-std=gnu++20"}], "defines": [{"define": "reanimated_EXPORTS"}], "includes": [{"backtrace": 7, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor"}, {"backtrace": 7, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric"}, {"backtrace": 7, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers"}, {"backtrace": 7, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations"}, {"backtrace": 7, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules"}, {"backtrace": 7, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime"}, {"backtrace": 7, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries"}, {"backtrace": 7, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems"}, {"backtrace": 7, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools"}, {"backtrace": 7, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp"}, {"backtrace": 8, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule"}, {"backtrace": 8, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon"}, {"backtrace": 8, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker"}, {"backtrace": 8, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx"}, {"backtrace": 8, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor"}, {"backtrace": 8, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga"}, {"backtrace": 8, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry"}, {"backtrace": 8, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core"}, {"backtrace": 3, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include"}, {"backtrace": 3, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include"}, {"backtrace": 3, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include"}, {"backtrace": 3, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include"}, {"backtrace": 3, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "reanimated::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Wl,--gc-sections -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "-llog", "role": "libraries"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\24bc47c525967d5c2a6bcc66e53ba8c8\\transformed\\jetified-react-android-0.73.8-debug\\prefab\\modules\\folly_runtime\\libs\\android.x86\\libfolly_runtime.so", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\24bc47c525967d5c2a6bcc66e53ba8c8\\transformed\\jetified-react-android-0.73.8-debug\\prefab\\modules\\glog\\libs\\android.x86\\libglog.so", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\24bc47c525967d5c2a6bcc66e53ba8c8\\transformed\\jetified-react-android-0.73.8-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\24bc47c525967d5c2a6bcc66e53ba8c8\\transformed\\jetified-react-android-0.73.8-debug\\prefab\\modules\\reactnativejni\\libs\\android.x86\\libreactnativejni.so", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\96f49ffbed5d5e97cf1c70574efe45b1\\transformed\\jetified-fbjni-0.5.1\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\34ed84f9f7d41443bb4242a6896db335\\transformed\\jetified-hermes-android-0.73.8-debug\\prefab\\modules\\libhermes\\libs\\android.x86\\libhermes.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\24bc47c525967d5c2a6bcc66e53ba8c8\\transformed\\jetified-react-android-0.73.8-debug\\prefab\\modules\\hermes_executor\\libs\\android.x86\\libhermes_executor.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "reanimated", "nameOnDisk": "libreanimated.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/AnimatedSensor/AnimatedSensorModule.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Fabric/PropsRegistry.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Fabric/ReanimatedCommitHook.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Fabric/ReanimatedCommitMarker.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Fabric/ReanimatedMountHook.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Fabric/ShadowTreeCloner.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/LayoutAnimations/LayoutAnimationsManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/LayoutAnimations/LayoutAnimationsProxy.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/LayoutAnimations/LayoutAnimationsUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/NativeModules/NativeReanimatedModule.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/NativeModules/NativeReanimatedModuleSpec.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/ReanimatedRuntime/RNRuntimeDecorator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/ReanimatedRuntime/ReanimatedHermesRuntime.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/ReanimatedRuntime/ReanimatedRuntime.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/ReanimatedRuntime/WorkletRuntime.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/ReanimatedRuntime/WorkletRuntimeDecorator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/ReanimatedRuntime/WorkletRuntimeRegistry.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Registries/EventHandlerRegistry.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/SharedItems/Shareables.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/AsyncQueue.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/FeaturesConfig.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/JSISerializer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/JSLogger.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/JSScheduler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/ReanimatedJSIUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/ReanimatedVersion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/UIRuntimeDecorator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/UIScheduler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/WorkletEventHandler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/AndroidLogger.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/AndroidUIScheduler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/JNIHelper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/LayoutAnimations.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/NativeProxy.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/OnLoad.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/TurboModule.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}