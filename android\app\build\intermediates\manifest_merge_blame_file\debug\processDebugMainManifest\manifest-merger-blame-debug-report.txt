1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.scriptsbundle.carspot"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-feature
11-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:5:5-7:36
12        android:name="android.hardware.camera"
12-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:6:9-47
13        android:required="false" />
13-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:7:9-33
14    <uses-feature
14-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:8:5-10:36
15        android:name="android.hardware.camera.front"
15-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:9:9-53
16        android:required="false" />
16-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:10:9-33
17
18    <uses-permission android:name="android.permission.INTERNET" />
18-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:12:5-67
18-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:12:22-64
19    <uses-permission android:name="android.permission.VIBRATE" />
19-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:13:5-66
19-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:13:22-63
20    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
20-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:14:5-80
20-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:14:22-78
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:16:5-79
21-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:16:22-76
22    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
22-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:17:5-78
22-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:17:22-75
23    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
23-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:18:5-79
23-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:18:22-76
24    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
24-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:19:5-81
24-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:19:22-78
25    <uses-permission android:name="android.permission.WAKE_LOCK" />
25-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:20:5-68
25-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:20:22-65
26    <uses-permission android:name="android.permission.CAMERA" />
26-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:21:5-65
26-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:21:22-62
27    <uses-permission android:name="android.permission.RECORD_AUDIO" />
27-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:23:5-71
27-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:23:22-68
28    <!--
29 Required only if your app needs to access images or photos
30         that other apps created.
31    -->
32    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
32-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:26:5-76
32-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:26:22-73
33    <!--
34 Required only if your app needs to access videos
35         that other apps created.
36    -->
37    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
37-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:30:5-75
37-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:30:22-72
38    <!--
39 If your app doesn't need to access media files that other apps created,
40         set the "maxSdkVersion" attribute to "28" instead.
41    -->
42    <uses-permission
42-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:34:5-36:38
43        android:name="android.permission.READ_EXTERNAL_STORAGE"
43-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:35:9-64
44        android:maxSdkVersion="32" />
44-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:36:9-35
45    <uses-permission
45-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:38:5-40:38
46        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
46-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:39:9-65
47        android:maxSdkVersion="29" />
47-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:40:9-35
48
49    <uses-feature
49-->[:react-native-camera] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-camera\android\build\intermediates\merged_manifest\mlkitDebug\processMlkitDebugManifest\AndroidManifest.xml:12:5-14:36
50        android:name="android.hardware.camera.autofocus"
50-->[:react-native-camera] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-camera\android\build\intermediates\merged_manifest\mlkitDebug\processMlkitDebugManifest\AndroidManifest.xml:13:9-57
51        android:required="false" />
51-->[:react-native-camera] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-camera\android\build\intermediates\merged_manifest\mlkitDebug\processMlkitDebugManifest\AndroidManifest.xml:14:9-33
52
53    <queries>
53-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
54        <intent>
54-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
55            <action android:name="android.media.action.IMAGE_CAPTURE" />
55-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-73
55-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-70
56        </intent>
57        <!-- Added to check the default browser that will host the AuthFlow. -->
58        <intent>
58-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:13:9-17:18
59            <action android:name="android.intent.action.VIEW" />
59-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
59-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
60
61            <data android:scheme="http" />
61-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
61-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
62        </intent> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
63        <package android:name="com.android.chrome" /> <!-- Needs to be explicitly declared on Android R+ -->
63-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:11:9-54
63-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:11:18-51
64        <package android:name="com.google.android.apps.maps" /> <!-- For browser content -->
64-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
64-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
65        <intent>
65-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:38:9-44:18
66            <action android:name="android.intent.action.VIEW" />
66-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
66-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
67
68            <category android:name="android.intent.category.BROWSABLE" />
68-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
68-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
69
70            <data android:scheme="https" />
70-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
70-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
71        </intent> <!-- End of browser content -->
72        <!-- For CustomTabsService -->
73        <intent>
73-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:47:9-49:18
74            <action android:name="android.support.customtabs.action.CustomTabsService" />
74-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:48:13-90
74-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:48:21-87
75        </intent>
76    </queries> <!-- Required to access Google Play Licensing -->
77    <uses-permission android:name="com.android.vending.CHECK_LICENSE" /> <!-- Required to check whether Wi-Fi is enabled -->
77-->[:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-73
77-->[:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-70
78    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
78-->[:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:5-76
78-->[:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:22-73
79    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
79-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:23:5-77
79-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:23:22-74
80    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
80-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
80-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
81    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
81-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:25:5-79
81-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:25:22-76
82    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
82-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:26:5-88
82-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:26:22-85
83    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
83-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:27:5-82
83-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:27:22-79
84    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
84-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7967c55ccf334bac24879478dcc135b4\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
84-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7967c55ccf334bac24879478dcc135b4\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
85
86    <uses-feature
86-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
87        android:glEsVersion="0x00020000"
87-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
88        android:required="true" />
88-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
89
90    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
90-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:29:5-83
90-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:29:22-80
91    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
91-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:26:5-110
91-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:26:22-107
92    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
92-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:28:5-77
92-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:28:22-74
93
94    <permission
94-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
95        android:name="com.scriptsbundle.carspot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
95-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
96        android:protectionLevel="signature" />
96-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
97
98    <uses-permission android:name="com.scriptsbundle.carspot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
98-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
98-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
99    <uses-permission android:name="com.android.vending.BILLING" /> <!-- for android -->
99-->[com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:10:5-67
99-->[com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:10:22-64
100    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
101    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
102    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
103    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
104    <!-- for Samsung -->
105    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
105-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
105-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
106    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
106-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
106-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
107    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
107-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
107-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
108    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
108-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
108-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
109    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
109-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
109-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
110    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
110-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
110-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
111    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
111-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
111-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
112    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
112-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
112-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
113    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
113-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
113-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
114    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
114-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
114-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
115    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
115-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
115-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
116    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
116-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
116-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
117    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
117-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
117-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
118    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
118-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
118-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
119    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
119-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
119-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
120    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
120-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
120-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
121
122    <application
122-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:42:5-121:19
123        android:name="com.scriptsbundle.carspot.MainApplication"
123-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:43:9-40
124        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
124-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
125        android:debuggable="true"
126        android:extractNativeLibs="false"
127        android:icon="@mipmap/ic_launcher"
127-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:44:9-43
128        android:label="@string/app_name"
128-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:45:9-41
129        android:roundIcon="@mipmap/ic_launcher"
129-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:46:9-48
130        android:supportsRtl="true"
130-->[com.facebook.android:facebook-login:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ccb0935089f1a8fb9e3f6cbba10cca93\transformed\jetified-facebook-login-7.1.0\AndroidManifest.xml:28:18-44
131        android:theme="@style/AppTheme"
131-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:47:9-40
132        android:usesCleartextTraffic="true" >
132-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml:6:9-44
133        <activity
133-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:48:9-59:20
134            android:name="com.scriptsbundle.carspot.MainActivity"
134-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:49:13-41
135            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
135-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:50:13-122
136            android:exported="true"
136-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:51:13-36
137            android:label="@string/app_name"
137-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:52:13-45
138            android:launchMode="singleTask"
138-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:53:13-44
139            android:windowSoftInputMode="adjustResize" >
139-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:54:13-55
140            <intent-filter>
140-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:55:13-58:29
141                <action android:name="android.intent.action.MAIN" />
141-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:56:17-69
141-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:56:25-66
142
143                <category android:name="android.intent.category.LAUNCHER" />
143-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:57:17-77
143-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:57:27-74
144            </intent-filter>
145        </activity>
146
147        <meta-data
147-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:61:9-63:71
148            android:name="com.google.android.geo.API_KEY"
148-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:62:13-58
149            android:value="AIzaSyAmnk3Obj7VDY1GfuZ_A6ep8voAGqJfayE" />
149-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:63:13-68
150        <meta-data
150-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:65:9-67:55
151            android:name="com.facebook.sdk.ApplicationId"
151-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:66:13-58
152            android:value="@string/facebook_app_id" />
152-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:67:13-52
153        <meta-data
153-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:68:9-70:37
154            android:name="com.dieam.reactnativepushnotification.notification_foreground"
154-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:69:13-89
155            android:value="false" /> <!-- Change the resource name to your App's accent color - or any other color you want -->
155-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:70:13-34
156        <meta-data
156-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:72:9-74:47
157            android:name="com.dieam.reactnativepushnotification.notification_color"
157-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:73:13-84
158            android:resource="@color/white" /> <!-- or @android:color/{name} to use a standard color -->
158-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:74:13-44
159        <meta-data
159-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:75:9-77:54
160            android:name="com.google.firebase.messaging.default_notification_icon"
160-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:76:13-83
161            android:resource="@mipmap/ic_launcher" />
161-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:77:13-51
162        <meta-data
162-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:78:9-80:71
163            android:name="com.dieam.reactnativepushnotification.default_notification_channel_id"
163-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:79:13-97
164            android:value="@string/default_notification_channel_id" />
164-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:80:13-68
165
166        <receiver android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationActions" />
166-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:82:9-108
166-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:82:19-105
167        <receiver android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationPublisher" />
167-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:83:9-110
167-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:83:19-107
168        <receiver
168-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:84:9-92:20
169            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationBootEventReceiver"
169-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:85:13-109
170            android:exported="true" >
170-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:86:13-36
171            <intent-filter>
171-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:87:13-91:29
172                <action android:name="android.intent.action.BOOT_COMPLETED" />
172-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:88:17-79
172-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:88:25-76
173                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
173-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:89:17-82
173-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:89:25-79
174                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
174-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:90:17-82
174-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:90:25-79
175            </intent-filter>
176        </receiver>
177
178        <service
178-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:94:9-100:19
179            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationListenerService"
179-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:95:13-107
180            android:exported="false" >
180-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:96:13-37
181            <intent-filter>
181-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:97:13-99:29
182                <action android:name="com.google.firebase.MESSAGING_EVENT" />
182-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:98:17-78
182-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:98:25-75
183            </intent-filter>
184        </service> <!-- Add these -->
185        <meta-data
185-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:102:9-104:44
186            android:name="com.supersami.foregroundservice.notification_channel_name"
186-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:103:13-85
187            android:value="Sticky Title" />
187-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:104:13-41
188        <meta-data
188-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:105:9-107:51
189            android:name="com.supersami.foregroundservice.notification_channel_description"
189-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:106:13-92
190            android:value="Sticky Description." />
190-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:107:13-48
191        <meta-data
191-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:108:9-110:46
192            android:name="com.supersami.foregroundservice.notification_color"
192-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:109:13-78
193            android:resource="@color/blue" /> <!-- <service android:name="com.supersami.foregroundservice.ForegroundService"></service> -->
193-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:110:13-43
194        <!-- <service android:name="com.supersami.foregroundservice.ForegroundServiceTask"></service> -->
195        <!-- End of content to add -->
196        <!-- Fix Google Play Services AD_SERVICES_CONFIG conflict -->
197        <property
197-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:117:9-120:48
198            android:name="android.adservices.AD_SERVICES_CONFIG"
198-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:118:13-65
199            android:resource="@xml/gma_ad_services_config" />
199-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:119:13-59
200
201        <meta-data
201-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:36
202            android:name="firebase_analytics_collection_enabled"
202-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-65
203            android:value="true" />
203-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
204        <meta-data
204-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:37
205            android:name="firebase_analytics_collection_deactivated"
205-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-69
206            android:value="false" />
206-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-34
207        <meta-data
207-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-20:36
208            android:name="google_analytics_adid_collection_enabled"
208-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-68
209            android:value="true" />
209-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-33
210        <meta-data
210-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-23:36
211            android:name="google_analytics_ssaid_collection_enabled"
211-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-69
212            android:value="true" />
212-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-33
213        <meta-data
213-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:9-26:36
214            android:name="google_analytics_automatic_screen_reporting_enabled"
214-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-79
215            android:value="true" />
215-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-33
216        <meta-data
216-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-29:36
217            android:name="google_analytics_default_allow_analytics_storage"
217-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-76
218            android:value="true" />
218-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-33
219        <meta-data
219-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:9-32:36
220            android:name="google_analytics_default_allow_ad_storage"
220-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-69
221            android:value="true" />
221-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-33
222        <meta-data
222-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-35:36
223            android:name="google_analytics_default_allow_ad_user_data"
223-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-71
224            android:value="true" />
224-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-33
225        <meta-data
225-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:9-38:36
226            android:name="google_analytics_default_allow_ad_personalization_signals"
226-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-85
227            android:value="true" />
227-->[:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-33
228
229        <service
229-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:40
230            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService"
230-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-103
231            android:exported="false" />
231-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
232        <service
232-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:19
233            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService"
233-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-95
234            android:exported="false" >
234-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
235            <intent-filter>
235-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:97:13-99:29
236                <action android:name="com.google.firebase.MESSAGING_EVENT" />
236-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:98:17-78
236-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:98:25-75
237            </intent-filter>
238        </service>
239
240        <receiver
240-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-30:20
241            android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
241-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-96
242            android:exported="true"
242-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
243            android:permission="com.google.android.c2dm.permission.SEND" >
243-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-73
244            <intent-filter>
244-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
245                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
245-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
245-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
246            </intent-filter>
247        </receiver>
248
249        <meta-data
249-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:36
250            android:name="firebase_messaging_auto_init_enabled"
250-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-64
251            android:value="true" />
251-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-33
252        <meta-data
252-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-37:32
253            android:name="com.google.firebase.messaging.default_notification_channel_id"
253-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-89
254            android:value="" />
254-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-29
255        <meta-data
255-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-40:47
256            android:name="com.google.firebase.messaging.default_notification_color"
256-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-84
257            android:resource="@color/white" />
257-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-44
258        <meta-data
258-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
259            android:name="app_data_collection_default_enabled"
259-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
260            android:value="true" />
260-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
261
262        <service
262-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
263            android:name="com.google.firebase.components.ComponentDiscoveryService"
263-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
264            android:directBootAware="true"
264-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
265            android:exported="false" >
265-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
266            <meta-data
266-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
267                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
267-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
268                android:value="com.google.firebase.components.ComponentRegistrar" />
268-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
269            <meta-data
269-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
270                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
270-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
271                android:value="com.google.firebase.components.ComponentRegistrar" />
271-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
272            <meta-data
272-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
273                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
273-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
274                android:value="com.google.firebase.components.ComponentRegistrar" />
274-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
275            <meta-data
275-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
276                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
276-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
277                android:value="com.google.firebase.components.ComponentRegistrar" />
277-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
278            <meta-data
278-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:37:13-39:85
279                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
279-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:38:17-139
280                android:value="com.google.firebase.components.ComponentRegistrar" />
280-->[com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:39:17-82
281            <meta-data
281-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
282                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
282-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
283                android:value="com.google.firebase.components.ComponentRegistrar" />
283-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
284            <meta-data
284-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
285                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
285-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
286                android:value="com.google.firebase.components.ComponentRegistrar" />
286-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
287            <meta-data
287-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
288                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
288-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
289                android:value="com.google.firebase.components.ComponentRegistrar" />
289-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
290            <meta-data
290-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ffc46a3fa056390226de8ebe20bc9b7b\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
291                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
291-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ffc46a3fa056390226de8ebe20bc9b7b\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
292                android:value="com.google.firebase.components.ComponentRegistrar" />
292-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ffc46a3fa056390226de8ebe20bc9b7b\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
293            <meta-data
293-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
294                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
294-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
295                android:value="com.google.firebase.components.ComponentRegistrar" />
295-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
296            <meta-data
296-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.8\transforms\49d835d1d30bde2057437f842afdd454\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
297                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
297-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.8\transforms\49d835d1d30bde2057437f842afdd454\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
298                android:value="com.google.firebase.components.ComponentRegistrar" />
298-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.8\transforms\49d835d1d30bde2057437f842afdd454\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
299        </service>
300
301        <provider
301-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
302            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
302-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
303            android:authorities="com.scriptsbundle.carspot.reactnativefirebaseappinitprovider"
303-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
304            android:exported="false"
304-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
305            android:initOrder="99" />
305-->[:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
306
307        <meta-data
307-->[:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:70
308            android:name="com.google.android.gms.ads.APPLICATION_ID"
308-->[:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-69
309            android:value="ca-app-pub-2596107136418753~3939898798" />
309-->[:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-67
310        <meta-data
310-->[:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:37
311            android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT"
311-->[:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-81
312            android:value="false" />
312-->[:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-34
313        <meta-data
313-->[:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-20:36
314            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
314-->[:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-83
315            android:value="true" />
315-->[:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-33
316        <meta-data
316-->[:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-23:36
317            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
317-->[:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-79
318            android:value="true" />
318-->[:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-33
319
320        <provider
320-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:9-24:20
321            android:name="com.reactnative.ivpusic.imagepicker.IvpusicImagePickerFileProvider"
321-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-94
322            android:authorities="com.scriptsbundle.carspot.provider"
322-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-60
323            android:exported="false"
323-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-37
324            android:grantUriPermissions="true" >
324-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-47
325            <meta-data
325-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:78
326                android:name="android.support.FILE_PROVIDER_PATHS"
326-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-67
327                android:resource="@xml/ivpusic_imagepicker_provider_paths" />
327-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:17-75
328        </provider>
329
330        <activity
330-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-28:72
331            android:name="com.yalantis.ucrop.UCropActivity"
331-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-60
332            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
332-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-69
333
334        <provider
334-->[:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
335            android:name="com.imagepicker.FileProvider"
335-->[:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-56
336            android:authorities="com.scriptsbundle.carspot.provider"
336-->[:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-60
337            android:exported="false"
337-->[:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
338            android:grantUriPermissions="true" >
338-->[:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
339            <meta-data
339-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:78
340                android:name="android.support.FILE_PROVIDER_PATHS"
340-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-67
341                android:resource="@xml/provider_paths" />
341-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:17-75
342        </provider>
343        <provider
343-->[:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
344            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
344-->[:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
345            android:authorities="com.scriptsbundle.carspot.fileprovider"
345-->[:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
346            android:exported="false"
346-->[:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
347            android:grantUriPermissions="true" >
347-->[:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
348            <meta-data
348-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:78
349                android:name="android.support.FILE_PROVIDER_PATHS"
349-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-67
350                android:resource="@xml/file_provider_paths" />
350-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:17-75
351        </provider>
352        <provider
352-->[:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-40:20
353            android:name="com.RNFetchBlob.Utils.FileProvider"
353-->[:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-62
354            android:authorities="com.scriptsbundle.carspot.provider"
354-->[:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-60
355            android:exported="false"
355-->[:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-37
356            android:grantUriPermissions="true" >
356-->[:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-47
357            <meta-data
357-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:78
358                android:name="android.support.FILE_PROVIDER_PATHS"
358-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-67
359                android:resource="@xml/provider_paths" />
359-->[:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:17-75
360        </provider>
361
362        <activity
362-->[com.facebook.react:react-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\24bc47c525967d5c2a6bcc66e53ba8c8\transformed\jetified-react-android-0.73.8-debug\AndroidManifest.xml:19:9-21:40
363            android:name="com.facebook.react.devsupport.DevSettingsActivity"
363-->[com.facebook.react:react-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\24bc47c525967d5c2a6bcc66e53ba8c8\transformed\jetified-react-android-0.73.8-debug\AndroidManifest.xml:20:13-77
364            android:exported="false" />
364-->[com.facebook.react:react-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\24bc47c525967d5c2a6bcc66e53ba8c8\transformed\jetified-react-android-0.73.8-debug\AndroidManifest.xml:21:13-37
365
366        <receiver
366-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
367            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
367-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
368            android:exported="true"
368-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
369            android:permission="com.google.android.c2dm.permission.SEND" >
369-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
370            <intent-filter>
370-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
371                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
371-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
371-->[:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
372            </intent-filter>
373
374            <meta-data
374-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
375                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
375-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
376                android:value="true" />
376-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
377        </receiver>
378        <!--
379             FirebaseMessagingService performs security checks at runtime,
380             but set to not exported to explicitly avoid allowing another app to call it.
381        -->
382        <service
382-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
383            android:name="com.google.firebase.messaging.FirebaseMessagingService"
383-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
384            android:directBootAware="true"
384-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
385            android:exported="false" >
385-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
386            <intent-filter android:priority="-500" >
386-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:97:13-99:29
387                <action android:name="com.google.firebase.MESSAGING_EVENT" />
387-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:98:17-78
387-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:98:25-75
388            </intent-filter>
389        </service>
390
391        <activity
391-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
392            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
392-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
393            android:excludeFromRecents="true"
393-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
394            android:exported="true"
394-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
395            android:launchMode="singleTask"
395-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
396            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
396-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
397            <intent-filter>
397-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
398                <action android:name="android.intent.action.VIEW" />
398-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
398-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
399
400                <category android:name="android.intent.category.DEFAULT" />
400-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
400-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
401                <category android:name="android.intent.category.BROWSABLE" />
401-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
401-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
402
403                <data
403-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
404                    android:host="firebase.auth"
404-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
405                    android:path="/"
405-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
406                    android:scheme="genericidp" />
406-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
407            </intent-filter>
408        </activity>
409        <activity
409-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
410            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
410-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
411            android:excludeFromRecents="true"
411-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
412            android:exported="true"
412-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
413            android:launchMode="singleTask"
413-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
414            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
414-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
415            <intent-filter>
415-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
416                <action android:name="android.intent.action.VIEW" />
416-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
416-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
417
418                <category android:name="android.intent.category.DEFAULT" />
418-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
418-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
419                <category android:name="android.intent.category.BROWSABLE" />
419-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
419-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
420
421                <data
421-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
422                    android:host="firebase.auth"
422-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
423                    android:path="/"
423-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
424                    android:scheme="recaptcha" />
424-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
425            </intent-filter>
426        </activity>
427        <activity
427-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:21:9-65:20
428            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
428-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:22:13-109
429            android:exported="true"
429-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:23:13-36
430            android:launchMode="singleTask" >
430-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:24:13-44
431            <intent-filter>
431-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:25:13-64:29
432                <action android:name="android.intent.action.VIEW" />
432-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
432-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
433
434                <category android:name="android.intent.category.DEFAULT" />
434-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
434-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
435                <category android:name="android.intent.category.BROWSABLE" />
435-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
435-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
436
437                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
438                <data
438-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
439                    android:host="link-accounts"
439-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
440                    android:pathPrefix="/com.scriptsbundle.carspot/authentication_return"
441                    android:scheme="stripe-auth" />
441-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
442
443                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
444                <data
444-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
445                    android:host="link-native-accounts"
445-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
446                    android:pathPrefix="/com.scriptsbundle.carspot/authentication_return"
447                    android:scheme="stripe-auth" />
447-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
448
449                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
450                <data
450-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
451                    android:host="link-accounts"
451-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
452                    android:path="/com.scriptsbundle.carspot/success"
452-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
453                    android:scheme="stripe-auth" />
453-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
454                <data
454-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
455                    android:host="link-accounts"
455-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
456                    android:path="/com.scriptsbundle.carspot/cancel"
456-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
457                    android:scheme="stripe-auth" />
457-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
458
459                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
460                <data
460-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
461                    android:host="native-redirect"
461-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
462                    android:pathPrefix="/com.scriptsbundle.carspot"
463                    android:scheme="stripe-auth" />
463-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
464
465                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
466                <data
466-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
467                    android:host="auth-redirect"
467-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
468                    android:pathPrefix="/com.scriptsbundle.carspot"
469                    android:scheme="stripe" />
469-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
470            </intent-filter>
471        </activity>
472        <activity
472-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:66:9-69:77
473            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
473-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:67:13-101
474            android:exported="false"
474-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:68:13-37
475            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
475-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:69:13-74
476        <activity
476-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:70:9-74:58
477            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
477-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:71:13-110
478            android:exported="false"
478-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:72:13-37
479            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
479-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:73:13-74
480            android:windowSoftInputMode="adjustResize" />
480-->[com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:74:13-55
481        <activity
481-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:8:9-11:69
482            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
482-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:9:13-80
483            android:exported="false"
483-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:10:13-37
484            android:theme="@style/StripePaymentSheetDefaultTheme" />
484-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:11:13-66
485        <activity
485-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:12:9-15:69
486            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
486-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:13:13-82
487            android:exported="false"
487-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:14:13-37
488            android:theme="@style/StripePaymentSheetDefaultTheme" />
488-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:15:13-66
489        <activity
489-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:16:9-19:69
490            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
490-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:17:13-82
491            android:exported="false"
491-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:18:13-37
492            android:theme="@style/StripePaymentSheetDefaultTheme" />
492-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:19:13-66
493        <activity
493-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:20:9-23:69
494            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
494-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:21:13-97
495            android:exported="false"
495-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:22:13-37
496            android:theme="@style/StripePaymentSheetDefaultTheme" />
496-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:23:13-66
497        <activity
497-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:24:9-27:69
498            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
498-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:25:13-118
499            android:exported="false"
499-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:26:13-37
500            android:theme="@style/StripePaymentSheetDefaultTheme" />
500-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:27:13-66
501        <activity
501-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:28:9-31:69
502            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
502-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:29:13-105
503            android:exported="false"
503-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:30:13-37
504            android:theme="@style/StripePaymentSheetDefaultTheme" />
504-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:31:13-66
505        <activity
505-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:32:9-35:69
506            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
506-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:33:13-82
507            android:exported="false"
507-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:34:13-37
508            android:theme="@style/StripePaymentSheetDefaultTheme" />
508-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:35:13-66
509        <activity
509-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:36:9-39:68
510            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
510-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:37:13-94
511            android:exported="false"
511-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:38:13-37
512            android:theme="@style/StripePayLauncherDefaultTheme" />
512-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:39:13-65
513        <activity
513-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:40:9-42:69
514            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
514-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:41:13-121
515            android:theme="@style/StripePaymentSheetDefaultTheme" />
515-->[com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:42:13-66
516        <activity
516-->[com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:8:9-13:61
517            android:name="com.stripe.android.link.LinkForegroundActivity"
517-->[com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:9:13-74
518            android:autoRemoveFromRecents="true"
518-->[com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:10:13-49
519            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
519-->[com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:11:13-115
520            android:launchMode="singleTop"
520-->[com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:12:13-43
521            android:theme="@style/StripeTransparentTheme" />
521-->[com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:13:13-58
522        <activity
522-->[com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:14:9-31:20
523            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
523-->[com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:15:13-79
524            android:autoRemoveFromRecents="true"
524-->[com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:16:13-49
525            android:exported="true"
525-->[com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:17:13-36
526            android:launchMode="singleInstance"
526-->[com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:18:13-48
527            android:theme="@style/StripeTransparentTheme" >
527-->[com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:19:13-58
528            <intent-filter>
528-->[com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:20:13-30:29
529                <action android:name="android.intent.action.VIEW" />
529-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
529-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
530
531                <category android:name="android.intent.category.DEFAULT" />
531-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
531-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
532                <category android:name="android.intent.category.BROWSABLE" />
532-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
532-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
533
534                <data
534-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
535                    android:host="complete"
535-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
536                    android:path="/com.scriptsbundle.carspot"
536-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
537                    android:scheme="link-popup" />
537-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
538            </intent-filter>
539        </activity>
540        <activity
540-->[com.stripe:payments-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\499ca54c5c83ca3892ae328d83ec7b58\transformed\jetified-payments-ui-core-20.48.6\AndroidManifest.xml:8:9-11:69
541            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
541-->[com.stripe:payments-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\499ca54c5c83ca3892ae328d83ec7b58\transformed\jetified-payments-ui-core-20.48.6\AndroidManifest.xml:9:13-80
542            android:exported="false"
542-->[com.stripe:payments-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\499ca54c5c83ca3892ae328d83ec7b58\transformed\jetified-payments-ui-core-20.48.6\AndroidManifest.xml:10:13-37
543            android:theme="@style/StripePaymentSheetDefaultTheme" />
543-->[com.stripe:payments-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\499ca54c5c83ca3892ae328d83ec7b58\transformed\jetified-payments-ui-core-20.48.6\AndroidManifest.xml:11:13-66
544        <activity
544-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:15:9-18:57
545            android:name="com.stripe.android.view.AddPaymentMethodActivity"
545-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:16:13-76
546            android:exported="false"
546-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:17:13-37
547            android:theme="@style/StripeDefaultTheme" />
547-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:18:13-54
548        <activity
548-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:19:9-22:57
549            android:name="com.stripe.android.view.PaymentMethodsActivity"
549-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:20:13-74
550            android:exported="false"
550-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:21:13-37
551            android:theme="@style/StripeDefaultTheme" />
551-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:22:13-54
552        <activity
552-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:23:9-26:57
553            android:name="com.stripe.android.view.PaymentFlowActivity"
553-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:24:13-71
554            android:exported="false"
554-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:25:13-37
555            android:theme="@style/StripeDefaultTheme" />
555-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:26:13-54
556        <activity
556-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:27:9-30:57
557            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
557-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:28:13-78
558            android:exported="false"
558-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:29:13-37
559            android:theme="@style/StripeDefaultTheme" />
559-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:30:13-54
560        <activity
560-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:31:9-34:61
561            android:name="com.stripe.android.view.PaymentRelayActivity"
561-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:32:13-72
562            android:exported="false"
562-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:33:13-37
563            android:theme="@style/StripeTransparentTheme" />
563-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:34:13-58
564        <!--
565        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
566        launched the browser Activity will also handle the return URL deep link.
567        -->
568        <activity
568-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:40:9-44:61
569            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
569-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:41:13-85
570            android:exported="false"
570-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:42:13-37
571            android:launchMode="singleTask"
571-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:43:13-44
572            android:theme="@style/StripeTransparentTheme" />
572-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:44:13-58
573        <activity
573-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:45:9-62:20
574            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
574-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:46:13-88
575            android:exported="true"
575-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:47:13-36
576            android:launchMode="singleTask"
576-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:48:13-44
577            android:theme="@style/StripeTransparentTheme" >
577-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:49:13-58
578            <intent-filter>
578-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:50:13-61:29
579                <action android:name="android.intent.action.VIEW" />
579-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
579-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
580
581                <category android:name="android.intent.category.DEFAULT" />
581-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
581-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
582                <category android:name="android.intent.category.BROWSABLE" />
582-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
582-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
583
584                <!-- Must match `DefaultReturnUrl#value`. -->
585                <data
585-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
586                    android:host="payment_return_url"
586-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
587                    android:path="/com.scriptsbundle.carspot"
587-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
588                    android:scheme="stripesdk" />
588-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
589            </intent-filter>
590        </activity>
591        <activity
591-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:63:9-66:57
592            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
592-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:64:13-114
593            android:exported="false"
593-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:65:13-37
594            android:theme="@style/StripeDefaultTheme" />
594-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:66:13-54
595        <activity
595-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:67:9-70:66
596            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
596-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:68:13-90
597            android:exported="false"
597-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:69:13-37
598            android:theme="@style/StripeGooglePayDefaultTheme" />
598-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:70:13-63
599        <activity
599-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:71:9-74:66
600            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
600-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:72:13-103
601            android:exported="false"
601-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:73:13-37
602            android:theme="@style/StripeGooglePayDefaultTheme" />
602-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:74:13-63
603        <activity
603-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:75:9-78:68
604            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
604-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:76:13-107
605            android:exported="false"
605-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:77:13-37
606            android:theme="@style/StripePayLauncherDefaultTheme" />
606-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:78:13-65
607        <activity
607-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:79:9-82:61
608            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
608-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:80:13-97
609            android:exported="false"
609-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:81:13-37
610            android:theme="@style/StripeTransparentTheme" />
610-->[com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:82:13-58
611        <activity
611-->[com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:30:9-33:66
612            android:name="com.facebook.FacebookActivity"
612-->[com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:31:13-57
613            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
613-->[com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:32:13-96
614            android:theme="@style/com_facebook_activity_theme" />
614-->[com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:33:13-63
615        <activity android:name="com.facebook.CustomTabMainActivity" />
615-->[com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:34:9-71
615-->[com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:34:19-68
616        <activity
616-->[com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:35:9-49:20
617            android:name="com.facebook.CustomTabActivity"
617-->[com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:36:13-58
618            android:exported="true" >
618-->[com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:37:13-36
619            <intent-filter>
619-->[com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:39:13-48:29
620                <action android:name="android.intent.action.VIEW" />
620-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
620-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
621
622                <category android:name="android.intent.category.DEFAULT" />
622-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
622-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
623                <category android:name="android.intent.category.BROWSABLE" />
623-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
623-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
624
625                <data
625-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
626                    android:host="cct.com.scriptsbundle.carspot"
626-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
627                    android:scheme="fbconnect" />
627-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
628            </intent-filter>
629        </activity>
630        <activity
630-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\8940ccec49f713bbc673b05e4c50cee9\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:8:9-11:54
631            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
631-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\8940ccec49f713bbc673b05e4c50cee9\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:9:13-81
632            android:exported="false"
632-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\8940ccec49f713bbc673b05e4c50cee9\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:10:13-37
633            android:theme="@style/Stripe3DS2Theme" />
633-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\8940ccec49f713bbc673b05e4c50cee9\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:11:13-51
634        <activity
634-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
635            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
635-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
636            android:excludeFromRecents="true"
636-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
637            android:exported="false"
637-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
638            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
638-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
639        <!--
640            Service handling Google Sign-In user revocation. For apps that do not integrate with
641            Google Sign-In, this service will never be started.
642        -->
643        <service
643-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
644            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
644-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
645            android:exported="true"
645-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
646            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
646-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
647            android:visibleToInstantApps="true" />
647-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
648
649        <meta-data
649-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\acfb150b60e6fd84ed3b72224008cbd0\transformed\jetified-android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
650            android:name="com.google.android.gms.version"
650-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\acfb150b60e6fd84ed3b72224008cbd0\transformed\jetified-android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
651            android:value="@integer/google_play_services_version" /> <!-- Needs to be explicitly declared on P+ -->
651-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\acfb150b60e6fd84ed3b72224008cbd0\transformed\jetified-android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
652        <uses-library
652-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
653            android:name="org.apache.http.legacy"
653-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
654            android:required="false" />
654-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
655
656        <provider
656-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
657            android:name="com.google.firebase.provider.FirebaseInitProvider"
657-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
658            android:authorities="com.scriptsbundle.carspot.firebaseinitprovider"
658-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
659            android:directBootAware="true"
659-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
660            android:exported="false"
660-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
661            android:initOrder="100" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
661-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
662        <activity
662-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:56:9-61:43
663            android:name="com.google.android.gms.ads.AdActivity"
663-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:57:13-65
664            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
664-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:58:13-122
665            android:exported="false"
665-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:59:13-37
666            android:theme="@android:style/Theme.Translucent" />
666-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:60:13-61
667
668        <provider
668-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:63:9-68:43
669            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
669-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:64:13-76
670            android:authorities="com.scriptsbundle.carspot.mobileadsinitprovider"
670-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:65:13-73
671            android:exported="false"
671-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:66:13-37
672            android:initOrder="100" />
672-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:67:13-36
673
674        <service
674-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:70:9-74:43
675            android:name="com.google.android.gms.ads.AdService"
675-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:71:13-64
676            android:enabled="true"
676-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:72:13-35
677            android:exported="false" />
677-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:73:13-37
678
679        <activity
679-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:76:9-80:43
680            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
680-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:77:13-82
681            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
681-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:78:13-122
682            android:exported="false" />
682-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:79:13-37
683        <activity
683-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:81:9-88:43
684            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
684-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:82:13-82
685            android:excludeFromRecents="true"
685-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:83:13-46
686            android:exported="false"
686-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:84:13-37
687            android:launchMode="singleTask"
687-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:85:13-44
688            android:taskAffinity=""
688-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:86:13-36
689            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
689-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:87:13-72
690
691        <receiver
691-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:29:9-33:20
692            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
692-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:30:13-85
693            android:enabled="true"
693-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:31:13-35
694            android:exported="false" >
694-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:32:13-37
695        </receiver>
696
697        <service
697-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:35:9-38:40
698            android:name="com.google.android.gms.measurement.AppMeasurementService"
698-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:36:13-84
699            android:enabled="true"
699-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:37:13-35
700            android:exported="false" />
700-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:38:13-37
701        <service
701-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:39:9-43:72
702            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
702-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:40:13-87
703            android:enabled="true"
703-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:41:13-35
704            android:exported="false"
704-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:42:13-37
705            android:permission="android.permission.BIND_JOB_SERVICE" />
705-->[com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:43:13-69
706
707        <uses-library
707-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\a612e69c9a697ea7a7c718c84a9fcb0f\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
708            android:name="android.ext.adservices"
708-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\a612e69c9a697ea7a7c718c84a9fcb0f\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
709            android:required="false" />
709-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\a612e69c9a697ea7a7c718c84a9fcb0f\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
710
711        <provider
711-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:31:9-39:20
712            android:name="androidx.startup.InitializationProvider"
712-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:32:13-67
713            android:authorities="com.scriptsbundle.carspot.androidx-startup"
713-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:33:13-68
714            android:exported="false" >
714-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:34:13-37
715            <meta-data
715-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:36:13-38:52
716                android:name="androidx.work.WorkManagerInitializer"
716-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:37:17-68
717                android:value="androidx.startup" />
717-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:38:17-49
718            <meta-data
718-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e5dace6fd9721fd9ce95f833a31d3f98\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
719                android:name="androidx.emoji2.text.EmojiCompatInitializer"
719-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e5dace6fd9721fd9ce95f833a31d3f98\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
720                android:value="androidx.startup" />
720-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e5dace6fd9721fd9ce95f833a31d3f98\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
721            <meta-data
721-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\60b3ba4c701bd4a76d15f2ec3f8f6141\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
722                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
722-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\60b3ba4c701bd4a76d15f2ec3f8f6141\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
723                android:value="androidx.startup" />
723-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\60b3ba4c701bd4a76d15f2ec3f8f6141\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
724            <meta-data
724-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
725                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
725-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
726                android:value="androidx.startup" />
726-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
727        </provider>
728
729        <service
729-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:41:9-46:35
730            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
730-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:42:13-88
731            android:directBootAware="false"
731-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:43:13-44
732            android:enabled="@bool/enable_system_alarm_service_default"
732-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:44:13-72
733            android:exported="false" />
733-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:45:13-37
734        <service
734-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:47:9-53:35
735            android:name="androidx.work.impl.background.systemjob.SystemJobService"
735-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:48:13-84
736            android:directBootAware="false"
736-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:49:13-44
737            android:enabled="@bool/enable_system_job_service_default"
737-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:50:13-70
738            android:exported="true"
738-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:51:13-36
739            android:permission="android.permission.BIND_JOB_SERVICE" />
739-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:52:13-69
740        <service
740-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:54:9-59:35
741            android:name="androidx.work.impl.foreground.SystemForegroundService"
741-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:55:13-81
742            android:directBootAware="false"
742-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:56:13-44
743            android:enabled="@bool/enable_system_foreground_service_default"
743-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:57:13-77
744            android:exported="false" />
744-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:58:13-37
745
746        <receiver
746-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:61:9-66:35
747            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
747-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:62:13-88
748            android:directBootAware="false"
748-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:63:13-44
749            android:enabled="true"
749-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:64:13-35
750            android:exported="false" />
750-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:65:13-37
751        <receiver
751-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:67:9-77:20
752            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
752-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:68:13-106
753            android:directBootAware="false"
753-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:69:13-44
754            android:enabled="false"
754-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:70:13-36
755            android:exported="false" >
755-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:71:13-37
756            <intent-filter>
756-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:73:13-76:29
757                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
757-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:74:17-87
757-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:74:25-84
758                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
758-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:75:17-90
758-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:75:25-87
759            </intent-filter>
760        </receiver>
761        <receiver
761-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:78:9-88:20
762            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
762-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:79:13-104
763            android:directBootAware="false"
763-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:80:13-44
764            android:enabled="false"
764-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:81:13-36
765            android:exported="false" >
765-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:82:13-37
766            <intent-filter>
766-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:84:13-87:29
767                <action android:name="android.intent.action.BATTERY_OKAY" />
767-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:85:17-77
767-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:85:25-74
768                <action android:name="android.intent.action.BATTERY_LOW" />
768-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:86:17-76
768-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:86:25-73
769            </intent-filter>
770        </receiver>
771        <receiver
771-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:89:9-99:20
772            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
772-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:90:13-104
773            android:directBootAware="false"
773-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:91:13-44
774            android:enabled="false"
774-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:92:13-36
775            android:exported="false" >
775-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:93:13-37
776            <intent-filter>
776-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:95:13-98:29
777                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
777-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:96:17-83
777-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:96:25-80
778                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
778-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:97:17-82
778-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:97:25-79
779            </intent-filter>
780        </receiver>
781        <receiver
781-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:100:9-109:20
782            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
782-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:101:13-103
783            android:directBootAware="false"
783-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:102:13-44
784            android:enabled="false"
784-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:103:13-36
785            android:exported="false" >
785-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:104:13-37
786            <intent-filter>
786-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:106:13-108:29
787                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
787-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:107:17-79
787-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:107:25-76
788            </intent-filter>
789        </receiver>
790        <receiver
790-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:110:9-121:20
791            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
791-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:111:13-88
792            android:directBootAware="false"
792-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:112:13-44
793            android:enabled="false"
793-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:113:13-36
794            android:exported="false" >
794-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:114:13-37
795            <intent-filter>
795-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:116:13-120:29
796                <action android:name="android.intent.action.BOOT_COMPLETED" />
796-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:88:17-79
796-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:88:25-76
797                <action android:name="android.intent.action.TIME_SET" />
797-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:118:17-73
797-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:118:25-70
798                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
798-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:119:17-81
798-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:119:25-78
799            </intent-filter>
800        </receiver>
801        <receiver
801-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:122:9-131:20
802            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
802-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:123:13-99
803            android:directBootAware="false"
803-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:124:13-44
804            android:enabled="@bool/enable_system_alarm_service_default"
804-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:125:13-72
805            android:exported="false" >
805-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:126:13-37
806            <intent-filter>
806-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:128:13-130:29
807                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
807-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:129:17-98
807-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:129:25-95
808            </intent-filter>
809        </receiver>
810        <receiver
810-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:132:9-142:20
811            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
811-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:133:13-78
812            android:directBootAware="false"
812-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:134:13-44
813            android:enabled="true"
813-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:135:13-35
814            android:exported="true"
814-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:136:13-36
815            android:permission="android.permission.DUMP" >
815-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:137:13-57
816            <intent-filter>
816-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:139:13-141:29
817                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
817-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:140:17-88
817-->[androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:140:25-85
818            </intent-filter>
819        </receiver>
820        <!--
821         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
822         with the application context. This config is merged in with the host app's manifest,
823         but there can only be one provider with the same authority activated at any given
824         point; so if the end user has two or more different apps that use Facebook SDK, only the
825         first one will be able to use the provider. To work around this problem, we use the
826         following placeholder in the authority to identify each host application as if it was
827         a completely different provider.
828        -->
829        <provider
829-->[com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:39:9-42:40
830            android:name="com.facebook.internal.FacebookInitProvider"
830-->[com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:40:13-70
831            android:authorities="com.scriptsbundle.carspot.FacebookInitProvider"
831-->[com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:41:13-72
832            android:exported="false" />
832-->[com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:42:13-37
833
834        <receiver
834-->[com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:44:9-50:20
835            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
835-->[com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:45:13-86
836            android:exported="false" >
836-->[com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:46:13-37
837            <intent-filter>
837-->[com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:47:13-49:29
838                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
838-->[com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:48:17-95
838-->[com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:48:25-92
839            </intent-filter>
840        </receiver>
841
842        <activity
842-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a204d524ffb17d8a6401a23642fcddaf\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
843            android:name="com.google.android.gms.common.api.GoogleApiActivity"
843-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a204d524ffb17d8a6401a23642fcddaf\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
844            android:exported="false"
844-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a204d524ffb17d8a6401a23642fcddaf\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
845            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
845-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a204d524ffb17d8a6401a23642fcddaf\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
846
847        <receiver
847-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
848            android:name="androidx.profileinstaller.ProfileInstallReceiver"
848-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
849            android:directBootAware="false"
849-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
850            android:enabled="true"
850-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
851            android:exported="true"
851-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
852            android:permission="android.permission.DUMP" >
852-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
853            <intent-filter>
853-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
854                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
854-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
854-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
855            </intent-filter>
856            <intent-filter>
856-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
857                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
857-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
857-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
858            </intent-filter>
859            <intent-filter>
859-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
860                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
860-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
860-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
861            </intent-filter>
862            <intent-filter>
862-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
863                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
863-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
863-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
864            </intent-filter>
865        </receiver>
866
867        <service
867-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
868            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
868-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
869            android:exported="false" >
869-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
870            <meta-data
870-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
871                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
871-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
872                android:value="cct" />
872-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
873        </service>
874        <service
874-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
875            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
875-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
876            android:exported="false"
876-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
877            android:permission="android.permission.BIND_JOB_SERVICE" >
877-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
878        </service>
879
880        <receiver
880-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
881            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
881-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
882            android:exported="false" />
882-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
883
884        <service
884-->[androidx.room:room-runtime:2.4.0-rc01] C:\Users\<USER>\.gradle\caches\8.8\transforms\d77f02961d99c6f7e756b85d7f13aec6\transformed\room-runtime-2.4.0-rc01\AndroidManifest.xml:25:9-28:40
885            android:name="androidx.room.MultiInstanceInvalidationService"
885-->[androidx.room:room-runtime:2.4.0-rc01] C:\Users\<USER>\.gradle\caches\8.8\transforms\d77f02961d99c6f7e756b85d7f13aec6\transformed\room-runtime-2.4.0-rc01\AndroidManifest.xml:26:13-74
886            android:directBootAware="true"
886-->[androidx.room:room-runtime:2.4.0-rc01] C:\Users\<USER>\.gradle\caches\8.8\transforms\d77f02961d99c6f7e756b85d7f13aec6\transformed\room-runtime-2.4.0-rc01\AndroidManifest.xml:27:13-43
887            android:exported="false" />
887-->[androidx.room:room-runtime:2.4.0-rc01] C:\Users\<USER>\.gradle\caches\8.8\transforms\d77f02961d99c6f7e756b85d7f13aec6\transformed\room-runtime-2.4.0-rc01\AndroidManifest.xml:28:13-37
888
889        <meta-data
889-->[com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:13:9-15:37
890            android:name="com.google.android.play.billingclient.version"
890-->[com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:14:13-73
891            android:value="3.0.0" />
891-->[com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:15:13-34
892
893        <activity
893-->[com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:17:9-20:75
894            android:name="com.android.billingclient.api.ProxyBillingActivity"
894-->[com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:18:13-78
895            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
895-->[com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:19:13-96
896            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
896-->[com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:20:13-72
897        <activity
897-->[com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:8:9-12:75
898            android:name="com.jakewharton.processphoenix.ProcessPhoenix"
898-->[com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:9:13-73
899            android:exported="false"
899-->[com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:10:13-37
900            android:process=":phoenix"
900-->[com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:11:13-39
901            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
901-->[com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:12:13-72
902
903        <meta-data
903-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c5c9221373f2f9a56186a39edf42b1cc\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
904            android:name="aia-compat-api-min-version"
904-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c5c9221373f2f9a56186a39edf42b1cc\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
905            android:value="1" />
905-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c5c9221373f2f9a56186a39edf42b1cc\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
906    </application>
907
908</manifest>
