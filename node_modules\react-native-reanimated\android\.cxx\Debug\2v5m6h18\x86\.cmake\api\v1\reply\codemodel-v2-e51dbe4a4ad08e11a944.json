{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Reanimated", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "reanimated::@6890427a1f51a3e7e1df", "jsonFile": "target-reanimated-Debug-9d0d0a6e3ea2cecfe508.json", "name": "reanimated", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "source": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android"}, "version": {"major": 2, "minor": 3}}