# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Reanimated
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86/
# =============================================================================
# Object build statements for SHARED_LIBRARY target reanimated


#############################################
# Order-only phony target for reanimated

build cmake_object_order_depends_target_reanimated: phony || CMakeFiles/reanimated.dir

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/AnimatedSensor/AnimatedSensorModule.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/AnimatedSensor/AnimatedSensorModule.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\AnimatedSensor\AnimatedSensorModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\AnimatedSensor
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Fabric/PropsRegistry.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Fabric/PropsRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Fabric\PropsRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Fabric
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Fabric/ReanimatedCommitHook.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Fabric/ReanimatedCommitHook.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Fabric\ReanimatedCommitHook.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Fabric
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Fabric/ReanimatedCommitMarker.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Fabric/ReanimatedCommitMarker.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Fabric\ReanimatedCommitMarker.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Fabric
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Fabric/ReanimatedMountHook.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Fabric/ReanimatedMountHook.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Fabric\ReanimatedMountHook.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Fabric
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Fabric/ShadowTreeCloner.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Fabric/ShadowTreeCloner.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Fabric\ShadowTreeCloner.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Fabric
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/LayoutAnimations/LayoutAnimationsManager.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/LayoutAnimations/LayoutAnimationsManager.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\LayoutAnimations\LayoutAnimationsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\LayoutAnimations
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/LayoutAnimations/LayoutAnimationsProxy.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/LayoutAnimations/LayoutAnimationsProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\LayoutAnimations\LayoutAnimationsProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\LayoutAnimations
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/LayoutAnimations/LayoutAnimationsUtils.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/LayoutAnimations/LayoutAnimationsUtils.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\LayoutAnimations\LayoutAnimationsUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\LayoutAnimations
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/NativeModules/NativeReanimatedModule.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/NativeModules/NativeReanimatedModule.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\NativeModules\NativeReanimatedModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\NativeModules
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/NativeModules/NativeReanimatedModuleSpec.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/NativeModules/NativeReanimatedModuleSpec.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\NativeModules\NativeReanimatedModuleSpec.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\NativeModules
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/ReanimatedRuntime/RNRuntimeDecorator.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/ReanimatedRuntime/RNRuntimeDecorator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\ReanimatedRuntime\RNRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\ReanimatedRuntime
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/ReanimatedRuntime/ReanimatedHermesRuntime.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/ReanimatedRuntime/ReanimatedHermesRuntime.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\ReanimatedRuntime\ReanimatedHermesRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\ReanimatedRuntime
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/ReanimatedRuntime/ReanimatedRuntime.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/ReanimatedRuntime/ReanimatedRuntime.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\ReanimatedRuntime\ReanimatedRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\ReanimatedRuntime
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/ReanimatedRuntime/WorkletRuntime.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/ReanimatedRuntime/WorkletRuntime.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\ReanimatedRuntime\WorkletRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\ReanimatedRuntime
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/ReanimatedRuntime/WorkletRuntimeDecorator.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/ReanimatedRuntime/WorkletRuntimeDecorator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\ReanimatedRuntime\WorkletRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\ReanimatedRuntime
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/ReanimatedRuntime/WorkletRuntimeRegistry.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/ReanimatedRuntime/WorkletRuntimeRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\ReanimatedRuntime\WorkletRuntimeRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\5a46067d0bed641e4005a8b119a656e1\cpp\ReanimatedRuntime
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Registries/EventHandlerRegistry.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Registries/EventHandlerRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Registries\EventHandlerRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Registries
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/SharedItems/Shareables.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/SharedItems/Shareables.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\SharedItems\Shareables.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\SharedItems
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/AsyncQueue.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/AsyncQueue.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools\AsyncQueue.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/FeaturesConfig.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/FeaturesConfig.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools\FeaturesConfig.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/JSISerializer.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/JSISerializer.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools\JSISerializer.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/JSLogger.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/JSLogger.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools\JSLogger.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/JSScheduler.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/JSScheduler.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools\JSScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/ReanimatedJSIUtils.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/ReanimatedJSIUtils.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools\ReanimatedJSIUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/ReanimatedVersion.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/ReanimatedVersion.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools\ReanimatedVersion.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/UIRuntimeDecorator.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/UIRuntimeDecorator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools\UIRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/UIScheduler.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/UIScheduler.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools\UIScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/WorkletEventHandler.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/Common/cpp/Tools/WorkletEventHandler.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools\WorkletEventHandler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6576f2db8d34274020ed0378874df611\Common\cpp\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/src/main/cpp/AndroidLogger.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp/AndroidLogger.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\src\main\cpp\AndroidLogger.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/src/main/cpp/AndroidUIScheduler.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp/AndroidUIScheduler.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\src\main\cpp\AndroidUIScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/src/main/cpp/JNIHelper.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp/JNIHelper.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\src\main\cpp\JNIHelper.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/src/main/cpp/LayoutAnimations.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp/LayoutAnimations.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\src\main\cpp\LayoutAnimations.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/src/main/cpp/NativeProxy.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp/NativeProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\src\main\cpp\NativeProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/src/main/cpp/OnLoad.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp/OnLoad.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\src\main\cpp\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb

build CMakeFiles/reanimated.dir/src/main/cpp/TurboModule.cpp.o: CXX_COMPILER__reanimated_Debug H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp/TurboModule.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\src\main\cpp\TurboModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry" -I"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target reanimated


#############################################
# Link the shared library ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.so

build ../../../../build/intermediates/cxx/Debug/2v5m6h18/obj/x86/libreanimated.so: CXX_SHARED_LIBRARY_LINKER__reanimated_Debug CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/AnimatedSensor/AnimatedSensorModule.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Fabric/PropsRegistry.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Fabric/ReanimatedCommitHook.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Fabric/ReanimatedCommitMarker.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Fabric/ReanimatedMountHook.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Fabric/ShadowTreeCloner.cpp.o CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/LayoutAnimations/LayoutAnimationsManager.cpp.o CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/LayoutAnimations/LayoutAnimationsProxy.cpp.o CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/LayoutAnimations/LayoutAnimationsUtils.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/NativeModules/NativeReanimatedModule.cpp.o CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/NativeModules/NativeReanimatedModuleSpec.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/ReanimatedRuntime/RNRuntimeDecorator.cpp.o CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/ReanimatedRuntime/ReanimatedHermesRuntime.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/ReanimatedRuntime/ReanimatedRuntime.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/ReanimatedRuntime/WorkletRuntime.cpp.o CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/ReanimatedRuntime/WorkletRuntimeDecorator.cpp.o CMakeFiles/reanimated.dir/5a46067d0bed641e4005a8b119a656e1/cpp/ReanimatedRuntime/WorkletRuntimeRegistry.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Registries/EventHandlerRegistry.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/SharedItems/Shareables.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/AsyncQueue.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/FeaturesConfig.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/JSISerializer.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/JSLogger.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/JSScheduler.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/ReanimatedJSIUtils.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/ReanimatedVersion.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/UIRuntimeDecorator.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/UIScheduler.cpp.o CMakeFiles/reanimated.dir/6576f2db8d34274020ed0378874df611/Common/cpp/Tools/WorkletEventHandler.cpp.o CMakeFiles/reanimated.dir/src/main/cpp/AndroidLogger.cpp.o CMakeFiles/reanimated.dir/src/main/cpp/AndroidUIScheduler.cpp.o CMakeFiles/reanimated.dir/src/main/cpp/JNIHelper.cpp.o CMakeFiles/reanimated.dir/src/main/cpp/LayoutAnimations.cpp.o CMakeFiles/reanimated.dir/src/main/cpp/NativeProxy.cpp.o CMakeFiles/reanimated.dir/src/main/cpp/OnLoad.cpp.o CMakeFiles/reanimated.dir/src/main/cpp/TurboModule.cpp.o | C$:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/libs/android.x86/libfolly_runtime.so C$:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/libs/android.x86/libglog.so C$:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/libs/android.x86/libjsi.so C$:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/libs/android.x86/libreactnativejni.so C$:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/libs/android.x86/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/libs/android.x86/libhermes.so C$:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/libs/android.x86/libhermes_executor.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Wl,--gc-sections -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = -llog  -landroid  C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/libs/android.x86/libfolly_runtime.so  C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/libs/android.x86/libglog.so  C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/libs/android.x86/libjsi.so  C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/libs/android.x86/libreactnativejni.so  C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/libs/android.x86/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/libs/android.x86/libhermes.so  C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/libs/android.x86/libhermes_executor.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\reanimated.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreanimated.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_FILE = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.so
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\2v5m6h18\obj\x86\libreanimated.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-reanimated\android\.cxx\Debug\2v5m6h18\x86" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-reanimated\android\.cxx\Debug\2v5m6h18\x86" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-reanimated\android" -B"H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-reanimated\android\.cxx\Debug\2v5m6h18\x86""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build libreanimated.so: phony ../../../../build/intermediates/cxx/Debug/2v5m6h18/obj/x86/libreanimated.so

build reanimated: phony ../../../../build/intermediates/cxx/Debug/2v5m6h18/obj/x86/libreanimated.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86

build all: phony ../../../../build/intermediates/cxx/Debug/2v5m6h18/obj/x86/libreanimated.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86/CMakeFiles/cmake.verify_globs | ../../../../CMakeLists.txt ../prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfigVersion.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/hermes-engine/hermes-engineConfig.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/hermes-engine/hermes-engineConfigVersion.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86/CMakeFiles/VerifyGlobs.cmake H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../../../../CMakeLists.txt ../prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfigVersion.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/hermes-engine/hermes-engineConfig.cmake ../prefab/x86/prefab/lib/i686-linux-android/cmake/hermes-engine/hermes-engineConfigVersion.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86/CMakeFiles/VerifyGlobs.cmake H$:/project$ App$ 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
