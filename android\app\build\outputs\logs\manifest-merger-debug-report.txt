-- Merging decision tree log ---
manifest
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:1:1-122:12
MERGED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:1:1-122:12
INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.react:flipper-integration:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b3cdaf90ed9e7607fbd3453910e23535\transformed\jetified-flipper-integration-0.73.8-debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_datetimepicker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-41:12
MERGED from [:react-native-firebase_auth] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-11:12
MERGED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-43:12
MERGED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-30:12
MERGED from [:react-native-google-signin_google-signin] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-masked-view_masked-view] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-masked-view\masked-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:stripe_stripe-react-native] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@stripe\stripe-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:progress-hud] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\progress-hud\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-camera] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-camera\android\build\intermediates\merged_manifest\mlkitDebug\processMlkitDebugManifest\AndroidManifest.xml:2:1-16:12
MERGED from [:react-native-document-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-fbsdk] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-fbsdk\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:1-26:12
MERGED from [:react-native-fs] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-26:12
MERGED from [:react-native-iap] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-iap\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-31:12
MERGED from [:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-maps] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-pager-view] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-push-notification] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-push-notification\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-restart] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-restart\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-splash-screen] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:react-native-svg] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-video] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-43:12
MERGED from [com.facebook.react:react-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\24bc47c525967d5c2a6bcc66e53ba8c8\transformed\jetified-react-android-0.73.8-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\dc29f1d4c09f8e62fa326536fcc9da5f\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:8:1-13:12
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\58637bc2ecc098dfcb6cc4731823901d\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\55e60681720c38c995babad5618744c7\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\7a6d015f4ae4d6f5ed67c459728068d2\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ac2f39191ee0d2720a50b457aaa3ad7e\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\20d855dab7acfc5afb6ab6745872b742\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\a0f8ce38c39a51af51a7ee0fd1bc0b43\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\182979b44a3099e07adf830027d18292\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\bb06f25714427226d8d108350d2d27ae\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ced0262165e05e63a40eb69bd95c4ed6\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\3f70c86535bc30887f550813764a37c6\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\f129142598bcf6dcfe04e8e9fd147760\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\12391c7ac461d3ecb54c051bcf01584a\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\7bf5307e48639f545c24316f36ad1db5\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\1b9ab0023f5e058bff55e44da1d16b06\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\a2fd45069320f32b4d2fe9de30b2fb1c\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\7edfad2b65ee436ffbc01f8a1d9db37f\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-ml-vision:19.0.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\f523e3879d10cc7c0b5a2d6ac70b8214\transformed\jetified-firebase-ml-vision-19.0.3\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.firebase:firebase-ml-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b7f90e3bf8f46914fc5dbf3df2f8e5b6\transformed\jetified-firebase-ml-common-17.0.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.firebase:firebase-analytics:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22fbcb627b715cb264d58f512c4e459d\transformed\jetified-firebase-analytics-21.6.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ffc46a3fa056390226de8ebe20bc9b7b\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:2:1-18:12
MERGED from [androidx.work:work-runtime-ktx:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\52f7fc0be487b2dec1215dcf1efa9e21\transformed\work-runtime-ktx-2.8.0-alpha01\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6c3b31ee1f1c5a29eb0ef2e76000dc1f\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:8:1-13:12
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a78cbc905f049799ab9c2201eded6126\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:8:1-16:12
MERGED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:2:1-77:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7967c55ccf334bac24879478dcc135b4\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.stripe:stripe-android:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\f1701b516252a4888cb87a6082d0910b\transformed\jetified-stripe-android-20.48.6\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:2:1-45:12
MERGED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:2:1-34:12
MERGED from [com.stripe:payments-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\499ca54c5c83ca3892ae328d83ec7b58\transformed\jetified-payments-ui-core-20.48.6\AndroidManifest.xml:2:1-14:12
MERGED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:2:1-85:12
MERGED from [com.stripe:stripe-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\5e57ddb08cbbb9da6e02d8310dba6746\transformed\jetified-stripe-ui-core-20.48.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\8264dd861346fbfaf783240e244db6fe\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\f02987291236f10cbb2b8d0e6bbec1e7\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\3d4f82309e240fee90e9ab8fe9f19697\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\bb3473eca38398e80532db300bb45479\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1163841bcbaabec611cae0af92312719\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\19d33ce22e7e54fa1b165a1588ffbe55\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.android:facebook-login:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ccb0935089f1a8fb9e3f6cbba10cca93\transformed\jetified-facebook-login-7.1.0\AndroidManifest.xml:21:1-31:12
MERGED from [com.facebook.android:facebook-share:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d865f13548302a73a53de3dbf6695a07\transformed\jetified-facebook-share-7.1.0\AndroidManifest.xml:21:1-31:12
MERGED from [com.github.yalantis:ucrop:2.2.6-native] C:\Users\<USER>\.gradle\caches\8.8\transforms\956ea9098a076acba1bb27e02bf57df6\transformed\jetified-ucrop-2.2.6-native\AndroidManifest.xml:2:1-11:12
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e487badd7baab0b19a34acd96ba96f9b\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.stripe:payments-model:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c2accc2cf677b0221c8e7b0d1323a879\transformed\jetified-payments-model-20.48.6\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:21:1-52:12
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\8940ccec49f713bbc673b05e4c50cee9\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ba53887e9a6e8b8732c220479c7c1a85\transformed\jetified-accompanist-themeadapter-appcompat-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\609ca4d0a0c5abd978531594251381bf\transformed\jetified-accompanist-themeadapter-material-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\281091c43d9388650658bf314482db12\transformed\jetified-accompanist-themeadapter-material3-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3a6a96e8289963d40f1afc605f8476ab\transformed\jetified-accompanist-themeadapter-core-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5df8311471b4ca92b951d823d5f8858\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\1dabbccd73948a4b51362e21ade3a497\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.stripe:hcaptcha:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\3e8ec890024071e0d95cc9adf0352de2\transformed\jetified-hcaptcha-20.48.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\97c1ea2a61583a8d0fe7e2dd6079563f\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ab6ae71e7f88bd42d9776bcab5bc4fac\transformed\jetified-glide-4.12.0\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\199d95da7992e4e6405dcd10361990e2\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\acfb150b60e6fd84ed3b72224008cbd0\transformed\jetified-android-maps-utils-3.8.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\cb8eb9a49d678251a5ec98e7bfa94431\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-wallet:19.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d8eebb74de0d7fbb38cc249344aa323d\transformed\jetified-play-services-wallet-19.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3bc4fac1260428196f71d6a86cee7dbb\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b67a90c6b7c403a72d6896768bea41fb\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-ml-vision-face-model:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\185b2733f0bc12503f45764c6f18f1f6\transformed\jetified-firebase-ml-vision-face-model-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\910e3874fa8791515d41456578118f7a\transformed\jetified-play-services-ads-23.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:17:1-95:12
MERGED from [com.google.android.ump:user-messaging-platform:2.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\cadbc2bcb614a448a6633b356923382e\transformed\jetified-user-messaging-platform-2.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\5b63ca9d9be54c0a56e8936c73771d07\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\585e3bf946276d1b6d1c2ef6f9f68b66\transformed\jetified-play-services-measurement-sdk-21.6.2\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\559079541e128ad4468f076890dc4b6d\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fdc5d9a7e7ee0a94fed18188ed55f7b\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\4102fcd3662fac6763fcf783188db415\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\03adfb43a1150684bfa81797dc37e5ac\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54e217aa02240ef5db6b028104674890\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\f2b41d068519ca3fbb27b067277ab434\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\3f7e9be1f0894820b6b64df24900f596\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.8\transforms\773608f0525ac60412c2144cff91b19b\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ce6aab6d00b341edc9ab226b46585c8\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-vision:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\c6f25759edac1f059ffe0a9e5c883838\transformed\jetified-play-services-vision-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-vision-image-label:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5a773daca67ae92635d664e72e9e551d\transformed\jetified-play-services-vision-image-label-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-vision-common:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5138f64048705b9dca966f7eefbfe26e\transformed\jetified-play-services-vision-common-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-clearcut:16.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e9091a4c11185e97626cf5a8a233f542\transformed\jetified-play-services-clearcut-16.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\9dd6a073ec8ccd4c56af8e9b0e418c0e\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.stripe:stripe-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\02d045e81e2e5aacbeb3636fe86e807b\transformed\jetified-stripe-core-20.48.6\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\77ca6b2d2cabb5f36685ef5662989809\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\a612e69c9a697ea7a7c718c84a9fcb0f\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:17:1-145:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\5a0345c7fe0a6259ad7f63100924629f\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\95eb98549619f516a95332920a1d1d7a\transformed\jetified-material3-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\95359c74a1aab42a6813d78a2673ca60\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\033a25b46b9b1053f5f7ebbbd4f5befb\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\fdc452286f6919e1119a89be242c4621\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-flowlayout:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\394034cca6176117aad6045576af9a22\transformed\jetified-accompanist-flowlayout-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\8f4b4237f06df9a7a5408f4d08d268ed\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\32c80cae2d6c3cc13fd64c21ef1888e2\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\5c419b533d652950de09c5d4a5e45839\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\4b1c9c7ef2a3a589dafb393ebb00e1c5\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:21:1-53:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d537ecd63f2d23c9dbfa4f267b50b9f0\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7abf47b3d92d386c803e00d737971aa4\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\4745ce2c0fbdac3be34e2e4b1c392a64\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c9c21bfa16b8b84366afe794c55ca5e4\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad7f4550d91972c7e5e904ac9382feb\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e5dace6fd9721fd9ce95f833a31d3f98\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\079b9417a6350a20dd1f18dfe54bdc17\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\0bb72b13f2d062f98130ac1e59d52521\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\83224c2b141e969aa6a46d5206e76ea5\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9323f389c91d2b10ccb21c8b3ca2b5a9\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\926d428588386c589a9cdf23dbaf1a95\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\389748478603b565f265d8d563a7ddb9\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\60b3ba4c701bd4a76d15f2ec3f8f6141\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\391e7c9c431b386b5609d4fb4b4c096e\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\4ec129595738df257ab5a47204ecbcba\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\bb36b52b5b9dc761275b34d6124e063a\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\821693d84926a3b9c1db92fa38bb2fb2\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\3a51d1264762048f03e7ee237180a26d\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\bac9bf8c2de3347d3ba93150e3315b44\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\9dacf6dd98b5d0cb683dcd824e55dcaf\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fb1c8cc43013655bd900f817803c60e\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\ac80df2fa48bbc1b8051dac2ed06ec59\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\9e15d4c299659a2bb166bc49aa5bc456\transformed\jetified-runtime-livedata-1.5.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\808d74a62ee88bcabad170aabf66139e\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\5b9ddaecbeb5b0b12bbaade8b25b7541\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9dc44400a91c940e476b15a3c58e4516\transformed\browser-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a15f2e25d436649c4ee6b79747b41060\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\c496f883bd39a8ef476c787fd863f5e1\transformed\jetified-media3-datasource-okhttp-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-ui:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d400a8ce23440948e9e963cb2e88601e\transformed\jetified-media3-ui-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-session:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\aac549ed611a93b4251a23b37d9c7120\transformed\jetified-media3-session-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\4b427d43a733ec16d662a2ca913951dc\transformed\jetified-media3-datasource-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\dc9bf9d1fa78b17f8b713974734c23a1\transformed\jetified-media3-extractor-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\10d4c1104915ce6363e6c302545e432b\transformed\jetified-media3-container-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\a3cc2d33b171413f70f164f181ff4093\transformed\jetified-media3-decoder-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\dce60367deb18a8a1140fb52054bf763\transformed\jetified-media3-database-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\2b4efcc79168ca2eaeea26b90b42ff96\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\9db59b2842e229df7f5dfba3412b22b2\transformed\jetified-media3-exoplayer-smoothstreaming-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\cbdb2e921fbacf3d372627671b1166c3\transformed\jetified-media3-exoplayer-dash-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\f173a6b8c0a37ba010b066050757b8bf\transformed\jetified-media3-exoplayer-hls-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\dfef5131d36ba74ab1e3b7f9010a681f\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\4873a9439313b8b4cadd6189092973bc\transformed\webkit-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\78e3bc54244a61677b5fe4b62d636a91\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a565d272b7578058a417063eba758591\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f8d63ccd1930913a3c494e14d577910f\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\764d60cddf5a992776eba69056e6d64b\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.8\transforms\fb5b9ad225c697f3f25e9ff4bd5d5314\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\eb5ce08d04422e18e4dd0af35373518f\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\07fbd07e5e94ca4ddf524b223631e7dd\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\0c0eb34b4818757caa3c32cf0f510b58\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0b3b7393b4f20c05b4062a924078fce\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a16ad89609c80134665092df7a218111\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\423de0f4249d36ed7641a304567bd0a1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\26efb1f875656a6e1410060dbbf5bf28\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-flags:16.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\72420ad68cf6bee29986ee1c0b55a067\transformed\jetified-play-services-flags-16.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-phenotype:16.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ace7f8df28d518780c7d509c5e145651\transformed\jetified-play-services-phenotype-16.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\761d44b95abfc1e99af79136afad4f5a\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a204d524ffb17d8a6401a23642fcddaf\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:16:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\40b3f0074940c7d584147e06c9883318\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\122f938dc76429b6594077e43f57853d\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3b05a79dc757089bd3e11c2ecc772615\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\2da1136e4048252736c76ead48f5aab8\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-viewbinding:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\456cadf12d63807bf7eacae3196f2eb1\transformed\jetified-ui-viewbinding-1.5.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ae4773805cdb372d4d87d20f5fa69ec\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\850ac915311c8663201faa8f9563e12c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\849754aeff620700a7d47cd5d24e4cd5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\80a766b3cab4a4618ff672fef38a8864\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\73574354209965a4df3f0dfed92026e0\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-ads-base:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3c68d217e26356dac6d63c0490e83629\transformed\jetified-play-services-ads-base-23.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6d15218ed2a783dc8bda866cac8fb229\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5d71564be708485c47df61371ab51781\transformed\jetified-play-services-measurement-base-21.6.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c3881f2403957b87d423ea84ef32c7e1\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\be7faaae0b77b37428a171f4b7845965\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\3fe076f7db557c0e6030e5fc5e989883\transformed\jetified-activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\e1357b38b634788fb305cf6e7bf40e47\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\e391d66a5bc7bf3d6f6d5b1f6fdcbb82\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7e8373b0b5d197410546a0ca8ec9d760\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.react:hermes-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\34ed84f9f7d41443bb4242a6896db335\transformed\jetified-hermes-android-0.73.8-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd69dbb422558778ba7c058644496819\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\2b5740a3bcd6ecd1fd495f2c6d11db3f\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\f7de06f5d42fc3708644a415b9e59b45\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.8\transforms\39373006e0d9ffcfd64929fcfb8e6fd5\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6cbd7c2b7eb50fc09c8e32a926a6bd38\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.8\transforms\49d835d1d30bde2057437f842afdd454\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\659e69316ba463afec76f2aec0c07e62\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f823c2eec254dd4e20c62de09f7ad11e\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1cda8c4455cdc2d456622575972f37d8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7ce07881f4986d6068fece34f151c378\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.4.0-rc01] C:\Users\<USER>\.gradle\caches\8.8\transforms\d77f02961d99c6f7e756b85d7f13aec6\transformed\room-runtime-2.4.0-rc01\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\5068c2ba939b39c96c066c0ae2c9cfe5\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\2985545437143040688cfa04abfa9c68\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b9d5eb0f2400cc5486c08a324af81fcb\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\600f834db4949ff1ba28df4abe6ec335\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f45668be23f37060689d2488f98aca44\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\35d04499e5271835bf83aa67eade37a1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\804a0b0dd36e8387b78f5ef0cd02a49c\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\415d07c60a8598f23c49f5783dd3a695\transformed\sqlite-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7bed02e8602f3bb4e66b423779b2c917\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\5d6fe5ef2b7ba5ea557b9f97fd520edf\transformed\jetified-annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [:react-native-linear-gradient] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\96f49ffbed5d5e97cf1c70574efe45b1\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7deaf87361275d021ec7c42cc3cb1594\transformed\jetified-soloader-0.9.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:2:1-23:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:2:1-52:12
MERGED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:2:1-15:12
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\8.8\transforms\6c9a981f257436ed0f0c041e4c97a4e3\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:1:1-3:12
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e29f5a34c3665678d0ddf044e8cd7a42\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c5c9221373f2f9a56186a39edf42b1cc\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:2:1-15:12
	package
		INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:1:11-69
uses-feature#android.hardware.camera
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:5:5-7:36
MERGED from [:react-native-camera] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-camera\android\build\intermediates\merged_manifest\mlkitDebug\processMlkitDebugManifest\AndroidManifest.xml:9:5-11:36
MERGED from [:react-native-camera] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-camera\android\build\intermediates\merged_manifest\mlkitDebug\processMlkitDebugManifest\AndroidManifest.xml:9:5-11:36
	android:required
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:7:9-33
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:6:9-47
uses-feature#android.hardware.camera.front
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:8:5-10:36
	android:required
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:10:9-33
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:9:9-53
uses-permission#android.permission.INTERNET
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:12:5-67
MERGED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:react-native-firebase_auth] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_auth] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-67
MERGED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a78cbc905f049799ab9c2201eded6126\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:13:5-67
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a78cbc905f049799ab9c2201eded6126\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:13:5-67
MERGED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:7:5-67
MERGED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7967c55ccf334bac24879478dcc135b4\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7967c55ccf334bac24879478dcc135b4\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fdc5d9a7e7ee0a94fed18188ed55f7b\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fdc5d9a7e7ee0a94fed18188ed55f7b\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:12:22-64
uses-permission#android.permission.VIBRATE
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:13:5-66
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:13:22-63
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:14:5-80
REJECTED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:27:5-81
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:14:22-78
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:16:5-79
MERGED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:react-native-firebase_auth] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-firebase_auth] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-79
MERGED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7967c55ccf334bac24879478dcc135b4\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7967c55ccf334bac24879478dcc135b4\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fdc5d9a7e7ee0a94fed18188ed55f7b\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fdc5d9a7e7ee0a94fed18188ed55f7b\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:24:5-79
MERGED from [com.stripe:stripe-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\02d045e81e2e5aacbeb3636fe86e807b\transformed\jetified-stripe-core-20.48.6\AndroidManifest.xml:7:5-79
MERGED from [com.stripe:stripe-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\02d045e81e2e5aacbeb3636fe86e807b\transformed\jetified-stripe-core-20.48.6\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:26:5-79
MERGED from [androidx.media3:media3-common:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\2b4efcc79168ca2eaeea26b90b42ff96\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\2b4efcc79168ca2eaeea26b90b42ff96\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\dfef5131d36ba74ab1e3b7f9010a681f\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\dfef5131d36ba74ab1e3b7f9010a681f\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:16:22-76
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:17:5-78
MERGED from [com.facebook.react:react-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\24bc47c525967d5c2a6bcc66e53ba8c8\transformed\jetified-react-android-0.73.8-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\24bc47c525967d5c2a6bcc66e53ba8c8\transformed\jetified-react-android-0.73.8-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:17:22-75
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:18:5-79
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:18:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:19:5-81
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:19:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:20:5-68
MERGED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-68
MERGED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-68
MERGED from [:react-native-firebase_auth] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-firebase_auth] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-68
MERGED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fdc5d9a7e7ee0a94fed18188ed55f7b\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fdc5d9a7e7ee0a94fed18188ed55f7b\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:25:5-68
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:20:22-65
uses-permission#android.permission.CAMERA
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:21:5-65
MERGED from [:react-native-camera] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-camera\android\build\intermediates\merged_manifest\mlkitDebug\processMlkitDebugManifest\AndroidManifest.xml:7:5-65
MERGED from [:react-native-camera] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-camera\android\build\intermediates\merged_manifest\mlkitDebug\processMlkitDebugManifest\AndroidManifest.xml:7:5-65
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:21:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:23:5-71
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:23:22-68
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:26:5-76
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:26:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:30:5-75
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:30:22-72
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:34:5-36:38
	android:maxSdkVersion
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:36:9-35
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:35:9-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:38:5-40:38
MERGED from [:react-native-fs] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:react-native-fs] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-81
MERGED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-81
MERGED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:5-81
MERGED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:5-81
	android:maxSdkVersion
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:40:9-35
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:39:9-65
application
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:42:5-121:19
MERGED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:42:5-121:19
MERGED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:42:5-121:19
INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml:5:5-8:50
MERGED from [:react-native-community_datetimepicker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-20
MERGED from [:react-native-community_datetimepicker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-20
MERGED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-39:19
MERGED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-39:19
MERGED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-41:19
MERGED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-41:19
MERGED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-28:19
MERGED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-28:19
MERGED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-24:19
MERGED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-24:19
MERGED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:5-29:19
MERGED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:5-29:19
MERGED from [:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-splash-screen] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-8:19
MERGED from [:react-native-splash-screen] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-8:19
MERGED from [:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:5-41:19
MERGED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:5-41:19
MERGED from [com.facebook.react:react-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\24bc47c525967d5c2a6bcc66e53ba8c8\transformed\jetified-react-android-0.73.8-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\24bc47c525967d5c2a6bcc66e53ba8c8\transformed\jetified-react-android-0.73.8-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-analytics:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22fbcb627b715cb264d58f512c4e459d\transformed\jetified-firebase-analytics-21.6.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22fbcb627b715cb264d58f512c4e459d\transformed\jetified-firebase-analytics-21.6.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ffc46a3fa056390226de8ebe20bc9b7b\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ffc46a3fa056390226de8ebe20bc9b7b\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:20:5-75:19
MERGED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:20:5-75:19
MERGED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:7:5-43:19
MERGED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:7:5-43:19
MERGED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:7:5-32:19
MERGED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:7:5-32:19
MERGED from [com.stripe:payments-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\499ca54c5c83ca3892ae328d83ec7b58\transformed\jetified-payments-ui-core-20.48.6\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\499ca54c5c83ca3892ae328d83ec7b58\transformed\jetified-payments-ui-core-20.48.6\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:14:5-83:19
MERGED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:14:5-83:19
MERGED from [com.facebook.android:facebook-login:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ccb0935089f1a8fb9e3f6cbba10cca93\transformed\jetified-facebook-login-7.1.0\AndroidManifest.xml:28:5-29:19
MERGED from [com.facebook.android:facebook-login:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ccb0935089f1a8fb9e3f6cbba10cca93\transformed\jetified-facebook-login-7.1.0\AndroidManifest.xml:28:5-29:19
MERGED from [com.facebook.android:facebook-share:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d865f13548302a73a53de3dbf6695a07\transformed\jetified-facebook-share-7.1.0\AndroidManifest.xml:28:5-29:19
MERGED from [com.facebook.android:facebook-share:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d865f13548302a73a53de3dbf6695a07\transformed\jetified-facebook-share-7.1.0\AndroidManifest.xml:28:5-29:19
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e487badd7baab0b19a34acd96ba96f9b\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:11:5-20
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e487badd7baab0b19a34acd96ba96f9b\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:11:5-20
MERGED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:29:5-50:19
MERGED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:29:5-50:19
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\8940ccec49f713bbc673b05e4c50cee9\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\8940ccec49f713bbc673b05e4c50cee9\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5df8311471b4ca92b951d823d5f8858\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5df8311471b4ca92b951d823d5f8858\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\1dabbccd73948a4b51362e21ade3a497\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\1dabbccd73948a4b51362e21ade3a497\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ab6ae71e7f88bd42d9776bcab5bc4fac\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ab6ae71e7f88bd42d9776bcab5bc4fac\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\acfb150b60e6fd84ed3b72224008cbd0\transformed\jetified-android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\acfb150b60e6fd84ed3b72224008cbd0\transformed\jetified-android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3bc4fac1260428196f71d6a86cee7dbb\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3bc4fac1260428196f71d6a86cee7dbb\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b67a90c6b7c403a72d6896768bea41fb\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b67a90c6b7c403a72d6896768bea41fb\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-ml-vision-face-model:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\185b2733f0bc12503f45764c6f18f1f6\transformed\jetified-firebase-ml-vision-face-model-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-ml-vision-face-model:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\185b2733f0bc12503f45764c6f18f1f6\transformed\jetified-firebase-ml-vision-face-model-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:53:5-93:19
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:53:5-93:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\5b63ca9d9be54c0a56e8936c73771d07\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\5b63ca9d9be54c0a56e8936c73771d07\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\585e3bf946276d1b6d1c2ef6f9f68b66\transformed\jetified-play-services-measurement-sdk-21.6.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\585e3bf946276d1b6d1c2ef6f9f68b66\transformed\jetified-play-services-measurement-sdk-21.6.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\559079541e128ad4468f076890dc4b6d\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\559079541e128ad4468f076890dc4b6d\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\4102fcd3662fac6763fcf783188db415\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\4102fcd3662fac6763fcf783188db415\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\03adfb43a1150684bfa81797dc37e5ac\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\03adfb43a1150684bfa81797dc37e5ac\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54e217aa02240ef5db6b028104674890\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54e217aa02240ef5db6b028104674890\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ce6aab6d00b341edc9ab226b46585c8\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ce6aab6d00b341edc9ab226b46585c8\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-vision:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\c6f25759edac1f059ffe0a9e5c883838\transformed\jetified-play-services-vision-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-vision:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\c6f25759edac1f059ffe0a9e5c883838\transformed\jetified-play-services-vision-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-vision-image-label:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5a773daca67ae92635d664e72e9e551d\transformed\jetified-play-services-vision-image-label-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-vision-image-label:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5a773daca67ae92635d664e72e9e551d\transformed\jetified-play-services-vision-image-label-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-vision-common:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5138f64048705b9dca966f7eefbfe26e\transformed\jetified-play-services-vision-common-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-vision-common:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5138f64048705b9dca966f7eefbfe26e\transformed\jetified-play-services-vision-common-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-clearcut:16.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e9091a4c11185e97626cf5a8a233f542\transformed\jetified-play-services-clearcut-16.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-clearcut:16.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e9091a4c11185e97626cf5a8a233f542\transformed\jetified-play-services-clearcut-16.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\9dd6a073ec8ccd4c56af8e9b0e418c0e\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\9dd6a073ec8ccd4c56af8e9b0e418c0e\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\a612e69c9a697ea7a7c718c84a9fcb0f\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\a612e69c9a697ea7a7c718c84a9fcb0f\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:30:5-143:19
MERGED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:28:5-51:19
MERGED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:28:5-51:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e5dace6fd9721fd9ce95f833a31d3f98\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e5dace6fd9721fd9ce95f833a31d3f98\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\60b3ba4c701bd4a76d15f2ec3f8f6141\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\60b3ba4c701bd4a76d15f2ec3f8f6141\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-phenotype:16.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ace7f8df28d518780c7d509c5e145651\transformed\jetified-play-services-phenotype-16.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-phenotype:16.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ace7f8df28d518780c7d509c5e145651\transformed\jetified-play-services-phenotype-16.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\761d44b95abfc1e99af79136afad4f5a\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\761d44b95abfc1e99af79136afad4f5a\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a204d524ffb17d8a6401a23642fcddaf\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a204d524ffb17d8a6401a23642fcddaf\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\73574354209965a4df3f0dfed92026e0\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\73574354209965a4df3f0dfed92026e0\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-ads-base:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3c68d217e26356dac6d63c0490e83629\transformed\jetified-play-services-ads-base-23.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-base:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3c68d217e26356dac6d63c0490e83629\transformed\jetified-play-services-ads-base-23.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6d15218ed2a783dc8bda866cac8fb229\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6d15218ed2a783dc8bda866cac8fb229\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5d71564be708485c47df61371ab51781\transformed\jetified-play-services-measurement-base-21.6.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5d71564be708485c47df61371ab51781\transformed\jetified-play-services-measurement-base-21.6.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c3881f2403957b87d423ea84ef32c7e1\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c3881f2403957b87d423ea84ef32c7e1\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd69dbb422558778ba7c058644496819\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd69dbb422558778ba7c058644496819\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.8\transforms\49d835d1d30bde2057437f842afdd454\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.8\transforms\49d835d1d30bde2057437f842afdd454\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.room:room-runtime:2.4.0-rc01] C:\Users\<USER>\.gradle\caches\8.8\transforms\d77f02961d99c6f7e756b85d7f13aec6\transformed\room-runtime-2.4.0-rc01\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.4.0-rc01] C:\Users\<USER>\.gradle\caches\8.8\transforms\d77f02961d99c6f7e756b85d7f13aec6\transformed\room-runtime-2.4.0-rc01\AndroidManifest.xml:24:5-29:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\600f834db4949ff1ba28df4abe6ec335\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\600f834db4949ff1ba28df4abe6ec335\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\35d04499e5271835bf83aa67eade37a1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\35d04499e5271835bf83aa67eade37a1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7deaf87361275d021ec7c42cc3cb1594\transformed\jetified-soloader-0.9.0\AndroidManifest.xml:11:5-20
MERGED from [com.facebook.soloader:soloader:0.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7deaf87361275d021ec7c42cc3cb1594\transformed\jetified-soloader-0.9.0\AndroidManifest.xml:11:5-20
MERGED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:12:5-21:19
MERGED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:12:5-21:19
MERGED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:7:5-13:19
MERGED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:7:5-13:19
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e29f5a34c3665678d0ddf044e8cd7a42\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e29f5a34c3665678d0ddf044e8cd7a42\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c5c9221373f2f9a56186a39edf42b1cc\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c5c9221373f2f9a56186a39edf42b1cc\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
	android:extractNativeLibs
		INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from [com.facebook.android:facebook-login:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ccb0935089f1a8fb9e3f6cbba10cca93\transformed\jetified-facebook-login-7.1.0\AndroidManifest.xml:28:18-44
	android:label
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:45:9-41
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:45:9-41
	tools:ignore
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:46:9-48
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:46:9-48
	tools:targetApi
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml:7:9-29
	android:icon
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:44:9-43
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:44:9-43
	android:theme
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:47:9-40
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:47:9-40
	android:usesCleartextTraffic
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml:6:9-44
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:43:9-40
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:43:9-40
activity#com.scriptsbundle.carspot.MainActivity
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:48:9-59:20
	android:label
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:52:13-45
	android:launchMode
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:53:13-44
	android:windowSoftInputMode
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:54:13-55
	android:exported
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:51:13-36
	android:configChanges
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:50:13-122
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:49:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:55:13-58:29
action#android.intent.action.MAIN
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:56:17-69
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:56:25-66
category#android.intent.category.LAUNCHER
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:57:17-77
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:57:27-74
meta-data#com.google.android.geo.API_KEY
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:61:9-63:71
	android:value
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:63:13-68
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:62:13-58
meta-data#com.facebook.sdk.ApplicationId
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:65:9-67:55
	android:value
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:67:13-52
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:66:13-58
meta-data#com.dieam.reactnativepushnotification.notification_foreground
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:68:9-70:37
	android:value
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:70:13-34
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:69:13-89
meta-data#com.dieam.reactnativepushnotification.notification_color
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:72:9-74:47
	android:resource
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:74:13-44
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:73:13-84
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:75:9-77:54
	android:resource
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:77:13-51
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:76:13-83
meta-data#com.dieam.reactnativepushnotification.default_notification_channel_id
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:78:9-80:71
	android:value
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:80:13-68
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:79:13-97
receiver#com.dieam.reactnativepushnotification.modules.RNPushNotificationActions
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:82:9-108
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:82:19-105
receiver#com.dieam.reactnativepushnotification.modules.RNPushNotificationPublisher
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:83:9-110
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:83:19-107
receiver#com.dieam.reactnativepushnotification.modules.RNPushNotificationBootEventReceiver
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:84:9-92:20
	android:exported
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:86:13-36
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:85:13-109
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:com.htc.intent.action.QUICKBOOT_POWERON
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:87:13-91:29
action#android.intent.action.BOOT_COMPLETED
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:88:17-79
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:88:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:89:17-82
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:89:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:90:17-82
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:90:25-79
service#com.dieam.reactnativepushnotification.modules.RNPushNotificationListenerService
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:94:9-100:19
	android:exported
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:96:13-37
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:95:13-107
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:97:13-99:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:98:17-78
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:98:25-75
meta-data#com.supersami.foregroundservice.notification_channel_name
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:102:9-104:44
	android:value
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:104:13-41
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:103:13-85
meta-data#com.supersami.foregroundservice.notification_channel_description
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:105:9-107:51
	android:value
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:107:13-48
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:106:13-92
meta-data#com.supersami.foregroundservice.notification_color
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:108:9-110:46
	android:resource
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:110:13-43
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:109:13-78
property#android.adservices.AD_SERVICES_CONFIG
ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:117:9-120:48
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:30:9-32:61
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:30:9-32:61
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:90:9-92:62
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:90:9-92:62
	android:resource
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:119:13-59
		REJECTED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:32:13-58
		REJECTED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:92:13-59
	tools:replace
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:120:13-45
	android:name
		ADDED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\main\AndroidManifest.xml:118:13-65
uses-sdk
INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml
INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml
MERGED from [com.facebook.react:flipper-integration:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b3cdaf90ed9e7607fbd3453910e23535\transformed\jetified-flipper-integration-0.73.8-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:flipper-integration:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b3cdaf90ed9e7607fbd3453910e23535\transformed\jetified-flipper-integration-0.73.8-debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_datetimepicker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_datetimepicker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_auth] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_auth] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-google-signin_google-signin] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-signin_google-signin] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-masked-view_masked-view] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-masked-view\masked-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-masked-view_masked-view] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-masked-view\masked-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stripe_stripe-react-native] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@stripe\stripe-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stripe_stripe-react-native] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@stripe\stripe-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:progress-hud] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\progress-hud\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:progress-hud] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\progress-hud\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-camera] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-camera\android\build\intermediates\merged_manifest\mlkitDebug\processMlkitDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-camera] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-camera\android\build\intermediates\merged_manifest\mlkitDebug\processMlkitDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-document-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-document-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fbsdk] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-fbsdk\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:5-44
MERGED from [:react-native-fbsdk] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-fbsdk\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:5-44
MERGED from [:react-native-fs] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fs] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-iap] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-iap\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-iap] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-iap\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-pager-view] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-pager-view] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-pager-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-push-notification] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-push-notification\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-push-notification] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-push-notification\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-restart] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-restart\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-restart] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-restart\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-splash-screen] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-splash-screen] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-video] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-video] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\24bc47c525967d5c2a6bcc66e53ba8c8\transformed\jetified-react-android-0.73.8-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\24bc47c525967d5c2a6bcc66e53ba8c8\transformed\jetified-react-android-0.73.8-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\dc29f1d4c09f8e62fa326536fcc9da5f\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\dc29f1d4c09f8e62fa326536fcc9da5f\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\58637bc2ecc098dfcb6cc4731823901d\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\58637bc2ecc098dfcb6cc4731823901d\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\55e60681720c38c995babad5618744c7\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\55e60681720c38c995babad5618744c7\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\7a6d015f4ae4d6f5ed67c459728068d2\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\7a6d015f4ae4d6f5ed67c459728068d2\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ac2f39191ee0d2720a50b457aaa3ad7e\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ac2f39191ee0d2720a50b457aaa3ad7e\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\20d855dab7acfc5afb6ab6745872b742\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\20d855dab7acfc5afb6ab6745872b742\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\a0f8ce38c39a51af51a7ee0fd1bc0b43\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\a0f8ce38c39a51af51a7ee0fd1bc0b43\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\182979b44a3099e07adf830027d18292\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\182979b44a3099e07adf830027d18292\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\bb06f25714427226d8d108350d2d27ae\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\bb06f25714427226d8d108350d2d27ae\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ced0262165e05e63a40eb69bd95c4ed6\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ced0262165e05e63a40eb69bd95c4ed6\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\3f70c86535bc30887f550813764a37c6\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\3f70c86535bc30887f550813764a37c6\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\f129142598bcf6dcfe04e8e9fd147760\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\f129142598bcf6dcfe04e8e9fd147760\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\12391c7ac461d3ecb54c051bcf01584a\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\12391c7ac461d3ecb54c051bcf01584a\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\7bf5307e48639f545c24316f36ad1db5\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\7bf5307e48639f545c24316f36ad1db5\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\1b9ab0023f5e058bff55e44da1d16b06\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\1b9ab0023f5e058bff55e44da1d16b06\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\a2fd45069320f32b4d2fe9de30b2fb1c\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\a2fd45069320f32b4d2fe9de30b2fb1c\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\7edfad2b65ee436ffbc01f8a1d9db37f\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\7edfad2b65ee436ffbc01f8a1d9db37f\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-ml-vision:19.0.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\f523e3879d10cc7c0b5a2d6ac70b8214\transformed\jetified-firebase-ml-vision-19.0.3\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-ml-vision:19.0.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\f523e3879d10cc7c0b5a2d6ac70b8214\transformed\jetified-firebase-ml-vision-19.0.3\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-ml-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b7f90e3bf8f46914fc5dbf3df2f8e5b6\transformed\jetified-firebase-ml-common-17.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-ml-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b7f90e3bf8f46914fc5dbf3df2f8e5b6\transformed\jetified-firebase-ml-common-17.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-analytics:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22fbcb627b715cb264d58f512c4e459d\transformed\jetified-firebase-analytics-21.6.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22fbcb627b715cb264d58f512c4e459d\transformed\jetified-firebase-analytics-21.6.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ffc46a3fa056390226de8ebe20bc9b7b\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ffc46a3fa056390226de8ebe20bc9b7b\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\52f7fc0be487b2dec1215dcf1efa9e21\transformed\work-runtime-ktx-2.8.0-alpha01\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime-ktx:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\52f7fc0be487b2dec1215dcf1efa9e21\transformed\work-runtime-ktx-2.8.0-alpha01\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6c3b31ee1f1c5a29eb0ef2e76000dc1f\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6c3b31ee1f1c5a29eb0ef2e76000dc1f\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a78cbc905f049799ab9c2201eded6126\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a78cbc905f049799ab9c2201eded6126\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7967c55ccf334bac24879478dcc135b4\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7967c55ccf334bac24879478dcc135b4\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-android:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\f1701b516252a4888cb87a6082d0910b\transformed\jetified-stripe-android-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-android:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\f1701b516252a4888cb87a6082d0910b\transformed\jetified-stripe-android-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\499ca54c5c83ca3892ae328d83ec7b58\transformed\jetified-payments-ui-core-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\499ca54c5c83ca3892ae328d83ec7b58\transformed\jetified-payments-ui-core-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\5e57ddb08cbbb9da6e02d8310dba6746\transformed\jetified-stripe-ui-core-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\5e57ddb08cbbb9da6e02d8310dba6746\transformed\jetified-stripe-ui-core-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\8264dd861346fbfaf783240e244db6fe\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\8264dd861346fbfaf783240e244db6fe\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\f02987291236f10cbb2b8d0e6bbec1e7\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\f02987291236f10cbb2b8d0e6bbec1e7\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\3d4f82309e240fee90e9ab8fe9f19697\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\3d4f82309e240fee90e9ab8fe9f19697\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\bb3473eca38398e80532db300bb45479\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\bb3473eca38398e80532db300bb45479\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1163841bcbaabec611cae0af92312719\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1163841bcbaabec611cae0af92312719\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\19d33ce22e7e54fa1b165a1588ffbe55\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\19d33ce22e7e54fa1b165a1588ffbe55\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.android:facebook-login:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ccb0935089f1a8fb9e3f6cbba10cca93\transformed\jetified-facebook-login-7.1.0\AndroidManifest.xml:24:5-26:41
MERGED from [com.facebook.android:facebook-login:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ccb0935089f1a8fb9e3f6cbba10cca93\transformed\jetified-facebook-login-7.1.0\AndroidManifest.xml:24:5-26:41
MERGED from [com.facebook.android:facebook-share:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d865f13548302a73a53de3dbf6695a07\transformed\jetified-facebook-share-7.1.0\AndroidManifest.xml:24:5-26:41
MERGED from [com.facebook.android:facebook-share:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d865f13548302a73a53de3dbf6695a07\transformed\jetified-facebook-share-7.1.0\AndroidManifest.xml:24:5-26:41
MERGED from [com.github.yalantis:ucrop:2.2.6-native] C:\Users\<USER>\.gradle\caches\8.8\transforms\956ea9098a076acba1bb27e02bf57df6\transformed\jetified-ucrop-2.2.6-native\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.yalantis:ucrop:2.2.6-native] C:\Users\<USER>\.gradle\caches\8.8\transforms\956ea9098a076acba1bb27e02bf57df6\transformed\jetified-ucrop-2.2.6-native\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e487badd7baab0b19a34acd96ba96f9b\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e487badd7baab0b19a34acd96ba96f9b\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.stripe:payments-model:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c2accc2cf677b0221c8e7b0d1323a879\transformed\jetified-payments-model-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-model:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c2accc2cf677b0221c8e7b0d1323a879\transformed\jetified-payments-model-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:25:5-27:41
MERGED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:25:5-27:41
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\8940ccec49f713bbc673b05e4c50cee9\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\8940ccec49f713bbc673b05e4c50cee9\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ba53887e9a6e8b8732c220479c7c1a85\transformed\jetified-accompanist-themeadapter-appcompat-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ba53887e9a6e8b8732c220479c7c1a85\transformed\jetified-accompanist-themeadapter-appcompat-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\609ca4d0a0c5abd978531594251381bf\transformed\jetified-accompanist-themeadapter-material-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\609ca4d0a0c5abd978531594251381bf\transformed\jetified-accompanist-themeadapter-material-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\281091c43d9388650658bf314482db12\transformed\jetified-accompanist-themeadapter-material3-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\281091c43d9388650658bf314482db12\transformed\jetified-accompanist-themeadapter-material3-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3a6a96e8289963d40f1afc605f8476ab\transformed\jetified-accompanist-themeadapter-core-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3a6a96e8289963d40f1afc605f8476ab\transformed\jetified-accompanist-themeadapter-core-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5df8311471b4ca92b951d823d5f8858\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5df8311471b4ca92b951d823d5f8858\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\1dabbccd73948a4b51362e21ade3a497\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\1dabbccd73948a4b51362e21ade3a497\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.stripe:hcaptcha:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\3e8ec890024071e0d95cc9adf0352de2\transformed\jetified-hcaptcha-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:hcaptcha:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\3e8ec890024071e0d95cc9adf0352de2\transformed\jetified-hcaptcha-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\97c1ea2a61583a8d0fe7e2dd6079563f\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\97c1ea2a61583a8d0fe7e2dd6079563f\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ab6ae71e7f88bd42d9776bcab5bc4fac\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ab6ae71e7f88bd42d9776bcab5bc4fac\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\199d95da7992e4e6405dcd10361990e2\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\199d95da7992e4e6405dcd10361990e2\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\acfb150b60e6fd84ed3b72224008cbd0\transformed\jetified-android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\acfb150b60e6fd84ed3b72224008cbd0\transformed\jetified-android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\cb8eb9a49d678251a5ec98e7bfa94431\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\cb8eb9a49d678251a5ec98e7bfa94431\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d8eebb74de0d7fbb38cc249344aa323d\transformed\jetified-play-services-wallet-19.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d8eebb74de0d7fbb38cc249344aa323d\transformed\jetified-play-services-wallet-19.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3bc4fac1260428196f71d6a86cee7dbb\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3bc4fac1260428196f71d6a86cee7dbb\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b67a90c6b7c403a72d6896768bea41fb\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b67a90c6b7c403a72d6896768bea41fb\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-ml-vision-face-model:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\185b2733f0bc12503f45764c6f18f1f6\transformed\jetified-firebase-ml-vision-face-model-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-ml-vision-face-model:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\185b2733f0bc12503f45764c6f18f1f6\transformed\jetified-firebase-ml-vision-face-model-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\910e3874fa8791515d41456578118f7a\transformed\jetified-play-services-ads-23.0.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\910e3874fa8791515d41456578118f7a\transformed\jetified-play-services-ads-23.0.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.ump:user-messaging-platform:2.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\cadbc2bcb614a448a6633b356923382e\transformed\jetified-user-messaging-platform-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:2.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\cadbc2bcb614a448a6633b356923382e\transformed\jetified-user-messaging-platform-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\5b63ca9d9be54c0a56e8936c73771d07\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\5b63ca9d9be54c0a56e8936c73771d07\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\585e3bf946276d1b6d1c2ef6f9f68b66\transformed\jetified-play-services-measurement-sdk-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\585e3bf946276d1b6d1c2ef6f9f68b66\transformed\jetified-play-services-measurement-sdk-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\559079541e128ad4468f076890dc4b6d\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\559079541e128ad4468f076890dc4b6d\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fdc5d9a7e7ee0a94fed18188ed55f7b\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fdc5d9a7e7ee0a94fed18188ed55f7b\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\4102fcd3662fac6763fcf783188db415\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\4102fcd3662fac6763fcf783188db415\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\03adfb43a1150684bfa81797dc37e5ac\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\03adfb43a1150684bfa81797dc37e5ac\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54e217aa02240ef5db6b028104674890\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54e217aa02240ef5db6b028104674890\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\f2b41d068519ca3fbb27b067277ab434\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\f2b41d068519ca3fbb27b067277ab434\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\3f7e9be1f0894820b6b64df24900f596\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\3f7e9be1f0894820b6b64df24900f596\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.8\transforms\773608f0525ac60412c2144cff91b19b\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.8\transforms\773608f0525ac60412c2144cff91b19b\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ce6aab6d00b341edc9ab226b46585c8\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ce6aab6d00b341edc9ab226b46585c8\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-vision:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\c6f25759edac1f059ffe0a9e5c883838\transformed\jetified-play-services-vision-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-vision:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\c6f25759edac1f059ffe0a9e5c883838\transformed\jetified-play-services-vision-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-vision-image-label:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5a773daca67ae92635d664e72e9e551d\transformed\jetified-play-services-vision-image-label-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-vision-image-label:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5a773daca67ae92635d664e72e9e551d\transformed\jetified-play-services-vision-image-label-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-vision-common:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5138f64048705b9dca966f7eefbfe26e\transformed\jetified-play-services-vision-common-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-vision-common:17.0.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5138f64048705b9dca966f7eefbfe26e\transformed\jetified-play-services-vision-common-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-clearcut:16.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e9091a4c11185e97626cf5a8a233f542\transformed\jetified-play-services-clearcut-16.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-clearcut:16.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e9091a4c11185e97626cf5a8a233f542\transformed\jetified-play-services-clearcut-16.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\9dd6a073ec8ccd4c56af8e9b0e418c0e\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\9dd6a073ec8ccd4c56af8e9b0e418c0e\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\02d045e81e2e5aacbeb3636fe86e807b\transformed\jetified-stripe-core-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\02d045e81e2e5aacbeb3636fe86e807b\transformed\jetified-stripe-core-20.48.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\77ca6b2d2cabb5f36685ef5662989809\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\77ca6b2d2cabb5f36685ef5662989809\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\a612e69c9a697ea7a7c718c84a9fcb0f\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\a612e69c9a697ea7a7c718c84a9fcb0f\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:21:5-23:41
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\5a0345c7fe0a6259ad7f63100924629f\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\5a0345c7fe0a6259ad7f63100924629f\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\95eb98549619f516a95332920a1d1d7a\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\95eb98549619f516a95332920a1d1d7a\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\95359c74a1aab42a6813d78a2673ca60\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\95359c74a1aab42a6813d78a2673ca60\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\033a25b46b9b1053f5f7ebbbd4f5befb\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\033a25b46b9b1053f5f7ebbbd4f5befb\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\fdc452286f6919e1119a89be242c4621\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\fdc452286f6919e1119a89be242c4621\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\394034cca6176117aad6045576af9a22\transformed\jetified-accompanist-flowlayout-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.32.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\394034cca6176117aad6045576af9a22\transformed\jetified-accompanist-flowlayout-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\8f4b4237f06df9a7a5408f4d08d268ed\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\8f4b4237f06df9a7a5408f4d08d268ed\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\32c80cae2d6c3cc13fd64c21ef1888e2\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\32c80cae2d6c3cc13fd64c21ef1888e2\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\5c419b533d652950de09c5d4a5e45839\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\5c419b533d652950de09c5d4a5e45839\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\4b1c9c7ef2a3a589dafb393ebb00e1c5\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\4b1c9c7ef2a3a589dafb393ebb00e1c5\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:24:5-26:41
MERGED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:24:5-26:41
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d537ecd63f2d23c9dbfa4f267b50b9f0\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d537ecd63f2d23c9dbfa4f267b50b9f0\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7abf47b3d92d386c803e00d737971aa4\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7abf47b3d92d386c803e00d737971aa4\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\4745ce2c0fbdac3be34e2e4b1c392a64\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\4745ce2c0fbdac3be34e2e4b1c392a64\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c9c21bfa16b8b84366afe794c55ca5e4\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c9c21bfa16b8b84366afe794c55ca5e4\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad7f4550d91972c7e5e904ac9382feb\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad7f4550d91972c7e5e904ac9382feb\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e5dace6fd9721fd9ce95f833a31d3f98\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e5dace6fd9721fd9ce95f833a31d3f98\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\079b9417a6350a20dd1f18dfe54bdc17\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\079b9417a6350a20dd1f18dfe54bdc17\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\0bb72b13f2d062f98130ac1e59d52521\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\0bb72b13f2d062f98130ac1e59d52521\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\83224c2b141e969aa6a46d5206e76ea5\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\83224c2b141e969aa6a46d5206e76ea5\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9323f389c91d2b10ccb21c8b3ca2b5a9\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9323f389c91d2b10ccb21c8b3ca2b5a9\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\926d428588386c589a9cdf23dbaf1a95\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\926d428588386c589a9cdf23dbaf1a95\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\389748478603b565f265d8d563a7ddb9\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\389748478603b565f265d8d563a7ddb9\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\60b3ba4c701bd4a76d15f2ec3f8f6141\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\60b3ba4c701bd4a76d15f2ec3f8f6141\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\391e7c9c431b386b5609d4fb4b4c096e\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\391e7c9c431b386b5609d4fb4b4c096e\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\4ec129595738df257ab5a47204ecbcba\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\4ec129595738df257ab5a47204ecbcba\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\bb36b52b5b9dc761275b34d6124e063a\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\bb36b52b5b9dc761275b34d6124e063a\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\821693d84926a3b9c1db92fa38bb2fb2\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\821693d84926a3b9c1db92fa38bb2fb2\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\3a51d1264762048f03e7ee237180a26d\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\3a51d1264762048f03e7ee237180a26d\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\bac9bf8c2de3347d3ba93150e3315b44\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\bac9bf8c2de3347d3ba93150e3315b44\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\9dacf6dd98b5d0cb683dcd824e55dcaf\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\9dacf6dd98b5d0cb683dcd824e55dcaf\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fb1c8cc43013655bd900f817803c60e\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fb1c8cc43013655bd900f817803c60e\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\ac80df2fa48bbc1b8051dac2ed06ec59\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\ac80df2fa48bbc1b8051dac2ed06ec59\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\9e15d4c299659a2bb166bc49aa5bc456\transformed\jetified-runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\9e15d4c299659a2bb166bc49aa5bc456\transformed\jetified-runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\808d74a62ee88bcabad170aabf66139e\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\808d74a62ee88bcabad170aabf66139e\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\5b9ddaecbeb5b0b12bbaade8b25b7541\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\5b9ddaecbeb5b0b12bbaade8b25b7541\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9dc44400a91c940e476b15a3c58e4516\transformed\browser-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9dc44400a91c940e476b15a3c58e4516\transformed\browser-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a15f2e25d436649c4ee6b79747b41060\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a15f2e25d436649c4ee6b79747b41060\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-datasource-okhttp:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\c496f883bd39a8ef476c787fd863f5e1\transformed\jetified-media3-datasource-okhttp-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\c496f883bd39a8ef476c787fd863f5e1\transformed\jetified-media3-datasource-okhttp-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d400a8ce23440948e9e963cb2e88601e\transformed\jetified-media3-ui-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d400a8ce23440948e9e963cb2e88601e\transformed\jetified-media3-ui-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\aac549ed611a93b4251a23b37d9c7120\transformed\jetified-media3-session-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\aac549ed611a93b4251a23b37d9c7120\transformed\jetified-media3-session-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\4b427d43a733ec16d662a2ca913951dc\transformed\jetified-media3-datasource-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\4b427d43a733ec16d662a2ca913951dc\transformed\jetified-media3-datasource-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\dc9bf9d1fa78b17f8b713974734c23a1\transformed\jetified-media3-extractor-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\dc9bf9d1fa78b17f8b713974734c23a1\transformed\jetified-media3-extractor-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\10d4c1104915ce6363e6c302545e432b\transformed\jetified-media3-container-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\10d4c1104915ce6363e6c302545e432b\transformed\jetified-media3-container-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\a3cc2d33b171413f70f164f181ff4093\transformed\jetified-media3-decoder-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\a3cc2d33b171413f70f164f181ff4093\transformed\jetified-media3-decoder-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\dce60367deb18a8a1140fb52054bf763\transformed\jetified-media3-database-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\dce60367deb18a8a1140fb52054bf763\transformed\jetified-media3-database-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\2b4efcc79168ca2eaeea26b90b42ff96\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\2b4efcc79168ca2eaeea26b90b42ff96\transformed\jetified-media3-common-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\9db59b2842e229df7f5dfba3412b22b2\transformed\jetified-media3-exoplayer-smoothstreaming-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\9db59b2842e229df7f5dfba3412b22b2\transformed\jetified-media3-exoplayer-smoothstreaming-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\cbdb2e921fbacf3d372627671b1166c3\transformed\jetified-media3-exoplayer-dash-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\cbdb2e921fbacf3d372627671b1166c3\transformed\jetified-media3-exoplayer-dash-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\f173a6b8c0a37ba010b066050757b8bf\transformed\jetified-media3-exoplayer-hls-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\f173a6b8c0a37ba010b066050757b8bf\transformed\jetified-media3-exoplayer-hls-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\dfef5131d36ba74ab1e3b7f9010a681f\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\dfef5131d36ba74ab1e3b7f9010a681f\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\4873a9439313b8b4cadd6189092973bc\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\4873a9439313b8b4cadd6189092973bc\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\78e3bc54244a61677b5fe4b62d636a91\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\78e3bc54244a61677b5fe4b62d636a91\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a565d272b7578058a417063eba758591\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a565d272b7578058a417063eba758591\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f8d63ccd1930913a3c494e14d577910f\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f8d63ccd1930913a3c494e14d577910f\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\764d60cddf5a992776eba69056e6d64b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\764d60cddf5a992776eba69056e6d64b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.8\transforms\fb5b9ad225c697f3f25e9ff4bd5d5314\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.8\transforms\fb5b9ad225c697f3f25e9ff4bd5d5314\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\eb5ce08d04422e18e4dd0af35373518f\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\eb5ce08d04422e18e4dd0af35373518f\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\07fbd07e5e94ca4ddf524b223631e7dd\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\07fbd07e5e94ca4ddf524b223631e7dd\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\0c0eb34b4818757caa3c32cf0f510b58\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\0c0eb34b4818757caa3c32cf0f510b58\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0b3b7393b4f20c05b4062a924078fce\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0b3b7393b4f20c05b4062a924078fce\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a16ad89609c80134665092df7a218111\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a16ad89609c80134665092df7a218111\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\423de0f4249d36ed7641a304567bd0a1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\423de0f4249d36ed7641a304567bd0a1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\26efb1f875656a6e1410060dbbf5bf28\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\26efb1f875656a6e1410060dbbf5bf28\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-flags:16.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\72420ad68cf6bee29986ee1c0b55a067\transformed\jetified-play-services-flags-16.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-flags:16.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\72420ad68cf6bee29986ee1c0b55a067\transformed\jetified-play-services-flags-16.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-phenotype:16.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ace7f8df28d518780c7d509c5e145651\transformed\jetified-play-services-phenotype-16.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-phenotype:16.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\ace7f8df28d518780c7d509c5e145651\transformed\jetified-play-services-phenotype-16.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\761d44b95abfc1e99af79136afad4f5a\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\761d44b95abfc1e99af79136afad4f5a\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a204d524ffb17d8a6401a23642fcddaf\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a204d524ffb17d8a6401a23642fcddaf\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\40b3f0074940c7d584147e06c9883318\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\40b3f0074940c7d584147e06c9883318\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\122f938dc76429b6594077e43f57853d\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\122f938dc76429b6594077e43f57853d\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3b05a79dc757089bd3e11c2ecc772615\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3b05a79dc757089bd3e11c2ecc772615\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\2da1136e4048252736c76ead48f5aab8\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\2da1136e4048252736c76ead48f5aab8\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\456cadf12d63807bf7eacae3196f2eb1\transformed\jetified-ui-viewbinding-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.5.4] C:\Users\<USER>\.gradle\caches\8.8\transforms\456cadf12d63807bf7eacae3196f2eb1\transformed\jetified-ui-viewbinding-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ae4773805cdb372d4d87d20f5fa69ec\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ae4773805cdb372d4d87d20f5fa69ec\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\850ac915311c8663201faa8f9563e12c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\850ac915311c8663201faa8f9563e12c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\849754aeff620700a7d47cd5d24e4cd5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\849754aeff620700a7d47cd5d24e4cd5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\80a766b3cab4a4618ff672fef38a8864\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\80a766b3cab4a4618ff672fef38a8864\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\73574354209965a4df3f0dfed92026e0\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\73574354209965a4df3f0dfed92026e0\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-ads-base:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3c68d217e26356dac6d63c0490e83629\transformed\jetified-play-services-ads-base-23.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\3c68d217e26356dac6d63c0490e83629\transformed\jetified-play-services-ads-base-23.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6d15218ed2a783dc8bda866cac8fb229\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6d15218ed2a783dc8bda866cac8fb229\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5d71564be708485c47df61371ab51781\transformed\jetified-play-services-measurement-base-21.6.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\5d71564be708485c47df61371ab51781\transformed\jetified-play-services-measurement-base-21.6.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c3881f2403957b87d423ea84ef32c7e1\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c3881f2403957b87d423ea84ef32c7e1\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\be7faaae0b77b37428a171f4b7845965\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\be7faaae0b77b37428a171f4b7845965\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\3fe076f7db557c0e6030e5fc5e989883\transformed\jetified-activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\3fe076f7db557c0e6030e5fc5e989883\transformed\jetified-activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\e1357b38b634788fb305cf6e7bf40e47\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\e1357b38b634788fb305cf6e7bf40e47\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\e391d66a5bc7bf3d6f6d5b1f6fdcbb82\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\e391d66a5bc7bf3d6f6d5b1f6fdcbb82\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7e8373b0b5d197410546a0ca8ec9d760\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7e8373b0b5d197410546a0ca8ec9d760\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.react:hermes-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\34ed84f9f7d41443bb4242a6896db335\transformed\jetified-hermes-android-0.73.8-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\34ed84f9f7d41443bb4242a6896db335\transformed\jetified-hermes-android-0.73.8-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd69dbb422558778ba7c058644496819\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd69dbb422558778ba7c058644496819\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\2b5740a3bcd6ecd1fd495f2c6d11db3f\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\2b5740a3bcd6ecd1fd495f2c6d11db3f\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\f7de06f5d42fc3708644a415b9e59b45\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\f7de06f5d42fc3708644a415b9e59b45\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.8\transforms\39373006e0d9ffcfd64929fcfb8e6fd5\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.8\transforms\39373006e0d9ffcfd64929fcfb8e6fd5\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6cbd7c2b7eb50fc09c8e32a926a6bd38\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6cbd7c2b7eb50fc09c8e32a926a6bd38\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.8\transforms\49d835d1d30bde2057437f842afdd454\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.8\transforms\49d835d1d30bde2057437f842afdd454\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\659e69316ba463afec76f2aec0c07e62\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\659e69316ba463afec76f2aec0c07e62\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f823c2eec254dd4e20c62de09f7ad11e\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f823c2eec254dd4e20c62de09f7ad11e\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1cda8c4455cdc2d456622575972f37d8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1cda8c4455cdc2d456622575972f37d8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7ce07881f4986d6068fece34f151c378\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7ce07881f4986d6068fece34f151c378\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.4.0-rc01] C:\Users\<USER>\.gradle\caches\8.8\transforms\d77f02961d99c6f7e756b85d7f13aec6\transformed\room-runtime-2.4.0-rc01\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.4.0-rc01] C:\Users\<USER>\.gradle\caches\8.8\transforms\d77f02961d99c6f7e756b85d7f13aec6\transformed\room-runtime-2.4.0-rc01\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\5068c2ba939b39c96c066c0ae2c9cfe5\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\5068c2ba939b39c96c066c0ae2c9cfe5\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\2985545437143040688cfa04abfa9c68\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\2985545437143040688cfa04abfa9c68\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b9d5eb0f2400cc5486c08a324af81fcb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b9d5eb0f2400cc5486c08a324af81fcb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\600f834db4949ff1ba28df4abe6ec335\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\600f834db4949ff1ba28df4abe6ec335\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f45668be23f37060689d2488f98aca44\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f45668be23f37060689d2488f98aca44\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\35d04499e5271835bf83aa67eade37a1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\35d04499e5271835bf83aa67eade37a1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\804a0b0dd36e8387b78f5ef0cd02a49c\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\804a0b0dd36e8387b78f5ef0cd02a49c\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\415d07c60a8598f23c49f5783dd3a695\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\415d07c60a8598f23c49f5783dd3a695\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7bed02e8602f3bb4e66b423779b2c917\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7bed02e8602f3bb4e66b423779b2c917\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\5d6fe5ef2b7ba5ea557b9f97fd520edf\transformed\jetified-annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\5d6fe5ef2b7ba5ea557b9f97fd520edf\transformed\jetified-annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [:react-native-linear-gradient] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-linear-gradient] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\96f49ffbed5d5e97cf1c70574efe45b1\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\96f49ffbed5d5e97cf1c70574efe45b1\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7deaf87361275d021ec7c42cc3cb1594\transformed\jetified-soloader-0.9.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7deaf87361275d021ec7c42cc3cb1594\transformed\jetified-soloader-0.9.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:6:5-8:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\8.8\transforms\6c9a981f257436ed0f0c041e4c97a4e3\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:2:2-70
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\8.8\transforms\6c9a981f257436ed0f0c041e4c97a4e3\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:2:2-70
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e29f5a34c3665678d0ddf044e8cd7a42\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e29f5a34c3665678d0ddf044e8cd7a42\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c5c9221373f2f9a56186a39edf42b1cc\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c5c9221373f2f9a56186a39edf42b1cc\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\android\app\src\debug\AndroidManifest.xml
meta-data#firebase_analytics_collection_enabled
ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:36
	android:value
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-65
meta-data#firebase_analytics_collection_deactivated
ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:37
	android:value
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-34
	android:name
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-69
meta-data#google_analytics_adid_collection_enabled
ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-20:36
	android:value
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-68
meta-data#google_analytics_ssaid_collection_enabled
ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-23:36
	android:value
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-69
meta-data#google_analytics_automatic_screen_reporting_enabled
ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:9-26:36
	android:value
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-79
meta-data#google_analytics_default_allow_analytics_storage
ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-29:36
	android:value
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-76
meta-data#google_analytics_default_allow_ad_storage
ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:9-32:36
	android:value
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-69
meta-data#google_analytics_default_allow_ad_user_data
ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:9-35:36
	android:value
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-71
meta-data#google_analytics_default_allow_ad_personalization_signals
ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:9-38:36
	android:value
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:13-33
	android:name
		ADDED from [:react-native-firebase_analytics] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\analytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-85
service#io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService
ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:40
	android:exported
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
	android:name
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-103
service#io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService
ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-21:19
	android:exported
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
	android:name
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-95
receiver#io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver
ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-30:20
	android:exported
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
	android:permission
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-73
	android:name
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-96
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-81
	android:name
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-78
meta-data#firebase_messaging_auto_init_enabled
ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-34:36
	android:value
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-33
	android:name
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-64
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-37:32
	android:value
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:13-29
	android:name
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-89
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-40:47
	android:resource
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-44
	android:name
		ADDED from [:react-native-firebase_messaging] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-84
meta-data#app_data_collection_default_enabled
ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
	android:value
		ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
	android:name
		ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ffc46a3fa056390226de8ebe20bc9b7b\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ffc46a3fa056390226de8ebe20bc9b7b\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.8\transforms\49d835d1d30bde2057437f842afdd454\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.8\transforms\49d835d1d30bde2057437f842afdd454\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
	android:exported
		ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	tools:targetApi
		ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-32
	android:directBootAware
		ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
	android:name
		ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
meta-data#com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar
ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
provider#io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider
ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
	android:authorities
		ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
	android:exported
		ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
	android:initOrder
		ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
	android:name
		ADDED from [:react-native-firebase_app] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
uses-feature#android.hardware.camera.autofocus
ADDED from [:react-native-camera] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-camera\android\build\intermediates\merged_manifest\mlkitDebug\processMlkitDebugManifest\AndroidManifest.xml:12:5-14:36
	android:required
		ADDED from [:react-native-camera] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-camera\android\build\intermediates\merged_manifest\mlkitDebug\processMlkitDebugManifest\AndroidManifest.xml:14:9-33
	android:name
		ADDED from [:react-native-camera] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-camera\android\build\intermediates\merged_manifest\mlkitDebug\processMlkitDebugManifest\AndroidManifest.xml:13:9-57
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-14:70
	android:value
		ADDED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-67
	android:name
		ADDED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-69
meta-data#com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT
ADDED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:37
	android:value
		ADDED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-34
	android:name
		ADDED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-81
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION
ADDED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-20:36
	android:value
		ADDED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-33
	android:name
		ADDED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-83
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING
ADDED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-23:36
	android:value
		ADDED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-33
	android:name
		ADDED from [:react-native-google-mobile-ads] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-google-mobile-ads\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-79
queries
ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
MERGED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:10:5-18:15
MERGED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:10:5-18:15
MERGED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:8:5-12:15
MERGED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:8:5-12:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:35:5-51:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:35:5-51:15
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-73
	android:name
		ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-70
provider#com.reactnative.ivpusic.imagepicker.IvpusicImagePickerFileProvider
ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:9-24:20
	android:grantUriPermissions
		ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-47
	android:authorities
		ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-60
	android:exported
		ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-37
	android:name
		ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-94
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:78
	android:resource
		ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:17-75
	android:name
		ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-67
activity#com.yalantis.ucrop.UCropActivity
ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-28:72
	android:theme
		ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-69
	android:name
		ADDED from [:react-native-image-crop-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-crop-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-60
provider#com.imagepicker.FileProvider
ADDED from [:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-60
	android:exported
		ADDED from [:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-image-picker] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-56
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
uses-permission#com.android.vending.CHECK_LICENSE
ADDED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-73
	android:name
		ADDED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-70
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:5-76
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a78cbc905f049799ab9c2201eded6126\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:5-76
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a78cbc905f049799ab9c2201eded6126\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:5-76
	android:name
		ADDED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:22-73
provider#com.RNFetchBlob.Utils.FileProvider
ADDED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-40:20
	android:grantUriPermissions
		ADDED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-47
	android:authorities
		ADDED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-60
	android:exported
		ADDED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [:rn-fetch-blob] H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-62
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\24bc47c525967d5c2a6bcc66e53ba8c8\transformed\jetified-react-android-0.73.8-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\24bc47c525967d5c2a6bcc66e53ba8c8\transformed\jetified-react-android-0.73.8-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.73.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\24bc47c525967d5c2a6bcc66e53ba8c8\transformed\jetified-react-android-0.73.8-debug\AndroidManifest.xml:20:13-77
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:23:22-74
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fdc5d9a7e7ee0a94fed18188ed55f7b\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\9fdc5d9a7e7ee0a94fed18188ed55f7b\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\d0fce4cc7074c4781c9f1720a339b9d9\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.iid.Registrar
ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fced80dc79f5de49958f62854e3f9dee\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6d15218ed2a783dc8bda866cac8fb229\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6d15218ed2a783dc8bda866cac8fb229\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\ef569796c91af74dc60f0f9b6a287164\transformed\jetified-play-services-measurement-sdk-api-21.6.2\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:27:22-79
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\556a4802ca4cf9ee614014c732dce64d\transformed\jetified-play-services-measurement-api-21.6.2\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1e6f442b7b7710869a0fcb132f8c639c\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\8ad07cfa4779cdac809073ff9f47572f\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ffc46a3fa056390226de8ebe20bc9b7b\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ffc46a3fa056390226de8ebe20bc9b7b\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\ffc46a3fa056390226de8ebe20bc9b7b\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
intent#action:name:android.intent.action.VIEW+data:scheme:http
ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:13:9-17:18
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity
ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:21:9-65:20
	android:launchMode
		ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:24:13-44
	android:exported
		ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:23:13-36
	android:name
		ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:22:13-109
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/${applicationId}/cancel+data:path:/${applicationId}/success+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}/authentication_return+data:pathPrefix:/${applicationId}/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:25:13-64:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/com.scriptsbundle.carspot/cancel+data:path:/com.scriptsbundle.carspot/success+data:pathPrefix:/com.scriptsbundle.carspot+data:pathPrefix:/com.scriptsbundle.carspot+data:pathPrefix:/com.scriptsbundle.carspot/authentication_return+data:pathPrefix:/com.scriptsbundle.carspot/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:25:13-64:29
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetActivity
ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:66:9-69:77
	android:exported
		ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:68:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:69:13-74
	android:name
		ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:67:13-101
activity#com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity
ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:70:9-74:58
	android:windowSoftInputMode
		ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:74:13-55
	android:exported
		ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:72:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:73:13-74
	android:name
		ADDED from [com.stripe:financial-connections:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\ff2cb920f006dbbdc04027534aa55fc1\transformed\jetified-financial-connections-20.48.6\AndroidManifest.xml:71:13-110
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7967c55ccf334bac24879478dcc135b4\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\7967c55ccf334bac24879478dcc135b4\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
activity#com.stripe.android.paymentsheet.PaymentSheetActivity
ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:8:9-11:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:9:13-80
activity#com.stripe.android.paymentsheet.PaymentOptionsActivity
ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:12:9-15:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:14:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:15:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:13:13-82
activity#com.stripe.android.customersheet.CustomerSheetActivity
ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:16:9-19:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:18:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:19:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:17:13-82
activity#com.stripe.android.paymentsheet.addresselement.AddressElementActivity
ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:20:9-23:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:22:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:21:13-97
activity#com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity
ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:24:9-27:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:27:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:25:13-118
activity#com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity
ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:28:9-31:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:30:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:31:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:29:13-105
activity#com.stripe.android.paymentsheet.ui.SepaMandateActivity
ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:32:9-35:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:35:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:33:13-82
activity#com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity
ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:36:9-39:68
	android:exported
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:38:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:37:13-94
activity#com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity
ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:40:9-42:69
	android:theme
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:42:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\1a92c44f7b137a8fa3f608bb45b4ca55\transformed\jetified-paymentsheet-20.48.6\AndroidManifest.xml:41:13-121
activity#com.stripe.android.link.LinkForegroundActivity
ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:8:9-13:61
	android:launchMode
		ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:12:13-43
	android:autoRemoveFromRecents
		ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:10:13-49
	android:configChanges
		ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:11:13-115
	android:theme
		ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:13:13-58
	android:name
		ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:9:13-74
activity#com.stripe.android.link.LinkRedirectHandlerActivity
ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:14:9-31:20
	android:launchMode
		ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:18:13-48
	android:autoRemoveFromRecents
		ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:16:13-49
	android:exported
		ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:17:13-36
	android:theme
		ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:19:13-58
	android:name
		ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:15:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/${applicationId}+data:scheme:link-popup
ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:20:13-30:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/com.scriptsbundle.carspot+data:scheme:link-popup
ADDED from [com.stripe:link:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\c8b58f984f0881f39054c6a601751b1a\transformed\jetified-link-20.48.6\AndroidManifest.xml:20:13-30:29
activity#com.stripe.android.ui.core.cardscan.CardScanActivity
ADDED from [com.stripe:payments-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\499ca54c5c83ca3892ae328d83ec7b58\transformed\jetified-payments-ui-core-20.48.6\AndroidManifest.xml:8:9-11:69
	android:exported
		ADDED from [com.stripe:payments-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\499ca54c5c83ca3892ae328d83ec7b58\transformed\jetified-payments-ui-core-20.48.6\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:payments-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\499ca54c5c83ca3892ae328d83ec7b58\transformed\jetified-payments-ui-core-20.48.6\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:payments-ui-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\499ca54c5c83ca3892ae328d83ec7b58\transformed\jetified-payments-ui-core-20.48.6\AndroidManifest.xml:9:13-80
package#com.android.chrome
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:11:9-54
	android:name
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:11:18-51
activity#com.stripe.android.view.AddPaymentMethodActivity
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:15:9-18:57
	android:exported
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:17:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:18:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:16:13-76
activity#com.stripe.android.view.PaymentMethodsActivity
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:19:9-22:57
	android:exported
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:22:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:20:13-74
activity#com.stripe.android.view.PaymentFlowActivity
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:23:9-26:57
	android:exported
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:26:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:24:13-71
activity#com.stripe.android.view.PaymentAuthWebViewActivity
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:27:9-30:57
	android:exported
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:29:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:30:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:28:13-78
activity#com.stripe.android.view.PaymentRelayActivity
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:31:9-34:61
	android:exported
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:33:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:34:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:32:13-72
activity#com.stripe.android.payments.StripeBrowserLauncherActivity
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:40:9-44:61
	android:launchMode
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:43:13-44
	android:exported
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:42:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:44:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:41:13-85
activity#com.stripe.android.payments.StripeBrowserProxyReturnActivity
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:45:9-62:20
	android:launchMode
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:48:13-44
	android:exported
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:47:13-36
	android:theme
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:49:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:46:13-88
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/${applicationId}+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:50:13-61:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/com.scriptsbundle.carspot+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:50:13-61:29
activity#com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:63:9-66:57
	android:exported
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:65:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:66:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:64:13-114
activity#com.stripe.android.googlepaylauncher.GooglePayLauncherActivity
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:67:9-70:66
	android:exported
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:69:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:70:13-63
	android:name
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:68:13-90
activity#com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:71:9-74:66
	android:exported
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:73:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:74:13-63
	android:name
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:72:13-103
activity#com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:75:9-78:68
	android:exported
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:77:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:78:13-65
	android:name
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:76:13-107
activity#com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity
ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:79:9-82:61
	android:exported
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:81:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:82:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.48.6] C:\Users\<USER>\.gradle\caches\8.8\transforms\e2b29406b8221abf28d86aafd40405e7\transformed\jetified-payments-core-20.48.6\AndroidManifest.xml:80:13-97
activity#com.facebook.FacebookActivity
ADDED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:30:9-33:66
	android:configChanges
		ADDED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:32:13-96
	android:theme
		ADDED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:33:13-63
	android:name
		ADDED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:31:13-57
activity#com.facebook.CustomTabMainActivity
ADDED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:34:9-71
	android:name
		ADDED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:34:19-68
activity#com.facebook.CustomTabActivity
ADDED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:35:9-49:20
	tools:node
		ADDED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:38:13-31
	android:exported
		ADDED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:37:13-36
	android:name
		ADDED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:36:13-58
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.${applicationId}+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:39:13-48:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.com.scriptsbundle.carspot+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e63080fb8d84f123d18f0d2588d3a687\transformed\jetified-facebook-common-7.1.0\AndroidManifest.xml:39:13-48:29
activity#com.stripe.android.stripe3ds2.views.ChallengeActivity
ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\8940ccec49f713bbc673b05e4c50cee9\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:8:9-11:54
	android:exported
		ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\8940ccec49f713bbc673b05e4c50cee9\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\8940ccec49f713bbc673b05e4c50cee9\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:11:13-51
	android:name
		ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\8940ccec49f713bbc673b05e4c50cee9\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:9:13-81
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\b40a5e9e2f4994513c4c2a271aeba2bb\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
meta-data#com.google.android.gms.version
ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\acfb150b60e6fd84ed3b72224008cbd0\transformed\jetified-android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c3881f2403957b87d423ea84ef32c7e1\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c3881f2403957b87d423ea84ef32c7e1\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\acfb150b60e6fd84ed3b72224008cbd0\transformed\jetified-android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
	android:name
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\acfb150b60e6fd84ed3b72224008cbd0\transformed\jetified-android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd43601db1f8d24097d9c25cd7f61dcc\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.8\transforms\4e509a7cf9610c431185688114b1f534\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:29:22-80
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:38:9-44:18
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:48:21-87
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:56:9-61:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:59:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:61:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:58:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:60:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:57:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:63:9-68:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:65:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:66:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:68:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:67:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:64:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:70:9-74:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:72:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:73:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:74:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:71:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:76:9-80:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:79:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:80:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:78:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:77:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:81:9-88:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:83:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:85:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:84:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:88:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:87:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:86:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\54addc6d71c414b8beef616f321d6c9a\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:82:13-82
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\2d088702983e41bdf065bd095200467b\transformed\jetified-play-services-measurement-impl-21.6.2\AndroidManifest.xml:26:5-110
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e29f5a34c3665678d0ddf044e8cd7a42\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e29f5a34c3665678d0ddf044e8cd7a42\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.6.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\22e4b09160c52d589e5b4330aeb5aa8b\transformed\jetified-play-services-measurement-21.6.2\AndroidManifest.xml:40:13-87
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\a612e69c9a697ea7a7c718c84a9fcb0f\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\a612e69c9a697ea7a7c718c84a9fcb0f\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.8\transforms\a612e69c9a697ea7a7c718c84a9fcb0f\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e5dace6fd9721fd9ce95f833a31d3f98\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e5dace6fd9721fd9ce95f833a31d3f98\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\60b3ba4c701bd4a76d15f2ec3f8f6141\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\60b3ba4c701bd4a76d15f2ec3f8f6141\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd69dbb422558778ba7c058644496819\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\fd69dbb422558778ba7c058644496819\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.0-alpha01] C:\Users\<USER>\.gradle\caches\8.8\transforms\051229061e649c8fc43cabedf6b216e6\transformed\work-runtime-2.8.0-alpha01\AndroidManifest.xml:140:25-85
provider#com.facebook.internal.FacebookInitProvider
ADDED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:39:9-42:40
	android:authorities
		ADDED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:41:13-72
	android:exported
		ADDED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:42:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:40:13-70
receiver#com.facebook.CurrentAccessTokenExpirationBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:44:9-50:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:46:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:45:13-86
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:47:13-49:29
action#com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:48:17-95
	android:name
		ADDED from [com.facebook.android:facebook-core:7.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\616425e6b72c05a64fdf680ec4a57183\transformed\jetified-facebook-core-7.1.0\AndroidManifest.xml:48:25-92
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e5dace6fd9721fd9ce95f833a31d3f98\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e5dace6fd9721fd9ce95f833a31d3f98\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e5dace6fd9721fd9ce95f833a31d3f98\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\60b3ba4c701bd4a76d15f2ec3f8f6141\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\60b3ba4c701bd4a76d15f2ec3f8f6141\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\60b3ba4c701bd4a76d15f2ec3f8f6141\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a204d524ffb17d8a6401a23642fcddaf\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a204d524ffb17d8a6401a23642fcddaf\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a204d524ffb17d8a6401a23642fcddaf\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a204d524ffb17d8a6401a23642fcddaf\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.scriptsbundle.carspot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.scriptsbundle.carspot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\68ac4d472dcb58d7dac3bcd049684b3b\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\351cef4b04ae8ac902049141f3142e91\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.8\transforms\49d835d1d30bde2057437f842afdd454\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.8\transforms\49d835d1d30bde2057437f842afdd454\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.8\transforms\49d835d1d30bde2057437f842afdd454\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\b4c06b38f5b56a992ea1740c48b7497e\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.8\transforms\29d1775b8e025d13e2fd56c182e871f1\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.4.0-rc01] C:\Users\<USER>\.gradle\caches\8.8\transforms\d77f02961d99c6f7e756b85d7f13aec6\transformed\room-runtime-2.4.0-rc01\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.4.0-rc01] C:\Users\<USER>\.gradle\caches\8.8\transforms\d77f02961d99c6f7e756b85d7f13aec6\transformed\room-runtime-2.4.0-rc01\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.4.0-rc01] C:\Users\<USER>\.gradle\caches\8.8\transforms\d77f02961d99c6f7e756b85d7f13aec6\transformed\room-runtime-2.4.0-rc01\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.4.0-rc01] C:\Users\<USER>\.gradle\caches\8.8\transforms\d77f02961d99c6f7e756b85d7f13aec6\transformed\room-runtime-2.4.0-rc01\AndroidManifest.xml:26:13-74
uses-permission#com.android.vending.BILLING
ADDED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:10:5-67
	android:name
		ADDED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:10:22-64
meta-data#com.google.android.play.billingclient.version
ADDED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:13:9-15:37
	android:value
		ADDED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:15:13-34
	android:name
		ADDED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:14:13-73
activity#com.android.billingclient.api.ProxyBillingActivity
ADDED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:17:9-20:75
	android:configChanges
		ADDED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:19:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:20:13-72
	android:name
		ADDED from [com.android.billingclient:billing:3.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\25bc6e3efd3875e863a1068d28a5ea35\transformed\jetified-billing-3.0.0\AndroidManifest.xml:18:13-78
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.8\transforms\f509bc2f46789d7d2077c11817cd8b38\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
activity#com.jakewharton.processphoenix.ProcessPhoenix
ADDED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:8:9-12:75
	android:process
		ADDED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:11:13-39
	android:exported
		ADDED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:12:13-72
	android:name
		ADDED from [com.jakewharton:process-phoenix:2.1.2] C:\Users\<USER>\.gradle\caches\8.8\transforms\1d90e72d67206311cab9d809de4e118f\transformed\jetified-process-phoenix-2.1.2\AndroidManifest.xml:9:13-73
meta-data#aia-compat-api-min-version
ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c5c9221373f2f9a56186a39edf42b1cc\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
	android:value
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c5c9221373f2f9a56186a39edf42b1cc\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
	android:name
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\c5c9221373f2f9a56186a39edf42b1cc\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
