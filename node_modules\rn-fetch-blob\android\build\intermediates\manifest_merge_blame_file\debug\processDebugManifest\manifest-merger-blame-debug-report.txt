1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.RNFetchBlob" >
4
5    <uses-sdk android:minSdkVersion="26" />
6
7    <!-- Required to access Google Play Licensing -->
8    <uses-permission android:name="com.android.vending.CHECK_LICENSE" />
8-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:5:5-73
8-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:5:22-70
9
10    <!-- Required to download files from Google Play -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:8:5-67
11-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:8:22-64
12
13    <!--
14         Required to keep CPU alive while downloading files
15        (NOT to keep screen awake)
16    -->
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:12:5-68
17-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:12:22-65
18
19    <!--
20         Required to poll the state of the network connection
21        and respond to changes
22    -->
23    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
23-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:16:5-79
23-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:16:22-76
24
25    <!-- Required to check whether Wi-Fi is enabled -->
26    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
26-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:19:5-76
26-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:19:22-73
27
28    <!-- Required to read and write the expansion files on shared storage -->
29    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
29-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:22:5-81
29-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:22:22-78
30
31    <application android:label="@string/app_name" >
31-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:24:5-35:19
31-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:24:18-50
32        <provider
32-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:26:9-34:20
33            android:name="com.RNFetchBlob.Utils.FileProvider"
33-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:27:13-62
34            android:authorities="${applicationId}.provider"
34-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:28:13-60
35            android:exported="false"
35-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:29:13-37
36            android:grantUriPermissions="true" >
36-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:30:13-47
37            <meta-data
37-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:31:13-33:58
38                android:name="android.support.FILE_PROVIDER_PATHS"
38-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:32:17-67
39                android:resource="@xml/provider_paths" />
39-->H:\project App 2025\codecanyon-CarSport\main-files\carspot\carspot\node_modules\rn-fetch-blob\android\src\main\AndroidManifest.xml:33:17-55
40        </provider>
41    </application>
42
43</manifest>
