{"info": {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, "cxxBuildFolder": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\6f3rov5l\\x86", "soFolder": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\6f3rov5l\\obj\\x86", "soRepublishFolder": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\build\\intermediates\\cmake\\debug\\obj\\x86", "abiPlatformVersion": 26, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_STL=c++_shared"], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\.cxx", "intermediatesBaseFolder": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\build\\intermediates", "intermediatesFolder": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx", "gradleModulePathName": ":react-native-screens", "moduleRootFolder": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android", "moduleBuildFile": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\build.gradle", "makeFile": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393", "ndkFolderBeforeSymLinking": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393", "ndkVersion": "25.1.8937393", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 19, "max": 33, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "x86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\android", "sdkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": ["C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar"], "prefabPackages": ["C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\24bc47c525967d5c2a6bcc66e53ba8c8\\transformed\\jetified-react-android-0.73.8-debug\\prefab", "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\96f49ffbed5d5e97cf1c70574efe45b1\\transformed\\jetified-fbjni-0.5.1\\prefab"], "prefabPackageConfigurations": [], "stlType": "c++_shared", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\6f3rov5l\\prefab\\x86", "isActiveAbi": true, "fullConfigurationHash": "6f3rov5l4n6c01s331u406k4f2o705o4p5612ywp4i468mj1z4r4o", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.5.2.\n#   - $NDK is the path to NDK 25.1.8937393.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HH:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-screens/android\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=26\n-DANDROID_PLATFORM=android-26\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-<PERSON><PERSON><PERSON>_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-screens/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-screens/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-DCMAKE_FIND_ROOT_PATH=H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-screens/android/.cxx/Debug/$HASH/prefab/$ABI/prefab\n-BH:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-screens/android/.cxx/Debug/$HASH/$ABI\n-GNinja\n-DANDROID_STL=c++_shared", "configurationArguments": ["-HH:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=26", "-DANDROID_PLATFORM=android-26", "-DANDROID_ABI=x86", "-DCMAKE_ANDROID_ARCH_ABI=x86", "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393", "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393", "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\6f3rov5l\\obj\\x86", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\6f3rov5l\\obj\\x86", "-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_FIND_ROOT_PATH=H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\6f3rov5l\\prefab\\x86\\prefab", "-BH:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\6f3rov5l\\x86", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_STL=c++_shared"], "stlLibraryFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "intermediatesParentFolder": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\6f3rov5l"}