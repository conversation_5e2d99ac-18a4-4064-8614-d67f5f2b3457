buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 26
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.8.0"
    }
    repositories {
        google()
        mavenCentral()
        jcenter()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.5.2")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
//        classpath 'com.google.gms:google-services:4.3.10' // Add this line

    }
}
allprojects {
    repositories {
        google()
        mavenCentral()
        jcenter() {
            content {
                includeModule("com.yqritc", "android-scalablevideoview")
                includeModule("com.kaopiz", "kprogresshud")
            }
        }
        maven { url "https://www.jitpack.io" }
        maven { url "https://oss.sonatype.org/content/repositories/snapshots/" }
        configurations.configureEach {
            resolutionStrategy {

                // use 0.9.0 to fix crash on Android 11
                force "com.facebook.soloader:soloader:0.9.0"
            }
        }
    }
}
apply plugin: "com.facebook.react.rootproject"
