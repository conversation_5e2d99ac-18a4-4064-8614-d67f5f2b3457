[{"level_": 0, "message_": "Start JSON generation. Platform version: 26 min SDK version: x86", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\x86\\android_gradle_build.json due to:", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Android\\\\Android Studio\\\\jbr\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  x86 ^\n  --os-version ^\n  26 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  25 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging10390214167135848422\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.8\\\\transforms\\\\24bc47c525967d5c2a6bcc66e53ba8c8\\\\transformed\\\\jetified-react-android-0.73.8-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.8\\\\transforms\\\\34ed84f9f7d41443bb4242a6896db335\\\\transformed\\\\jetified-hermes-android-0.73.8-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.8\\\\transforms\\\\96f49ffbed5d5e97cf1c70574efe45b1\\\\transformed\\\\jetified-fbjni-0.5.1\\\\prefab\"\n", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\x86'", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\x86'", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HH:\\\\project App 2025\\\\codecanyon-CarSport\\\\main-files\\\\carspot\\\\carspot\\\\node_modules\\\\react-native-reanimated\\\\android\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=26\" ^\n  \"-DANDROID_PLATFORM=android-26\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\25.1.8937393\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\25.1.8937393\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\25.1.8937393\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=H:\\\\project App 2025\\\\codecanyon-CarSport\\\\main-files\\\\carspot\\\\carspot\\\\node_modules\\\\react-native-reanimated\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2v5m6h18\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=H:\\\\project App 2025\\\\codecanyon-CarSport\\\\main-files\\\\carspot\\\\carspot\\\\node_modules\\\\react-native-reanimated\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2v5m6h18\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=H:\\\\project App 2025\\\\codecanyon-CarSport\\\\main-files\\\\carspot\\\\carspot\\\\node_modules\\\\react-native-reanimated\\\\android\\\\.cxx\\\\Debug\\\\2v5m6h18\\\\prefab\\\\x86\\\\prefab\" ^\n  \"-BH:\\\\project App 2025\\\\codecanyon-CarSport\\\\main-files\\\\carspot\\\\carspot\\\\node_modules\\\\react-native-reanimated\\\\android\\\\.cxx\\\\Debug\\\\2v5m6h18\\\\x86\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DREACT_NATIVE_MINOR_VERSION=73\" ^\n  \"-DANDROID_TOOLCHAIN=clang\" ^\n  \"-DREACT_NATIVE_DIR=H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native\" ^\n  \"-DJS_RUNTIME=hermes\" ^\n  \"-DJS_RUNTIME_DIR=H:\\\\project App 2025\\\\codecanyon-CarSport\\\\main-files\\\\carspot\\\\carspot\\\\node_modules\\\\react-native\\\\sdks\\\\hermes\" ^\n  \"-DIS_NEW_ARCHITECTURE_ENABLED=false\" ^\n  \"-DIS_REANIMATED_EXAMPLE_APP=false\" ^\n  \"-DREANIMATED_VERSION=3.13.0\" ^\n  \"-DHERMES_ENABLE_DEBUGGER=1\"\n", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HH:\\\\project App 2025\\\\codecanyon-CarSport\\\\main-files\\\\carspot\\\\carspot\\\\node_modules\\\\react-native-reanimated\\\\android\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=26\" ^\n  \"-DANDROID_PLATFORM=android-26\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\25.1.8937393\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\25.1.8937393\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\25.1.8937393\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=H:\\\\project App 2025\\\\codecanyon-CarSport\\\\main-files\\\\carspot\\\\carspot\\\\node_modules\\\\react-native-reanimated\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2v5m6h18\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=H:\\\\project App 2025\\\\codecanyon-CarSport\\\\main-files\\\\carspot\\\\carspot\\\\node_modules\\\\react-native-reanimated\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2v5m6h18\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=H:\\\\project App 2025\\\\codecanyon-CarSport\\\\main-files\\\\carspot\\\\carspot\\\\node_modules\\\\react-native-reanimated\\\\android\\\\.cxx\\\\Debug\\\\2v5m6h18\\\\prefab\\\\x86\\\\prefab\" ^\n  \"-BH:\\\\project App 2025\\\\codecanyon-CarSport\\\\main-files\\\\carspot\\\\carspot\\\\node_modules\\\\react-native-reanimated\\\\android\\\\.cxx\\\\Debug\\\\2v5m6h18\\\\x86\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DREACT_NATIVE_MINOR_VERSION=73\" ^\n  \"-DANDROID_TOOLCHAIN=clang\" ^\n  \"-DREACT_NATIVE_DIR=H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native\" ^\n  \"-DJS_RUNTIME=hermes\" ^\n  \"-DJS_RUNTIME_DIR=H:\\\\project App 2025\\\\codecanyon-CarSport\\\\main-files\\\\carspot\\\\carspot\\\\node_modules\\\\react-native\\\\sdks\\\\hermes\" ^\n  \"-DIS_NEW_ARCHITECTURE_ENABLED=false\" ^\n  \"-DIS_REANIMATED_EXAMPLE_APP=false\" ^\n  \"-DREANIMATED_VERSION=3.13.0\" ^\n  \"-DHERMES_ENABLE_DEBUGGER=1\"\n", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\x86\\compile_commands.json.bin normally", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\x86\\compile_commands.json to H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\tools\\debug\\x86\\compile_commands.json", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]