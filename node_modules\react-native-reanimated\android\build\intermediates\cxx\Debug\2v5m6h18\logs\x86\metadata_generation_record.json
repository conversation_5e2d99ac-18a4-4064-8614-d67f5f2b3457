[{"level_": 0, "message_": "Start JSON generation. Platform version: 26 min SDK version: x86", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2v5m6h18\\x86\\android_gradle_build.json' was up-to-date", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]