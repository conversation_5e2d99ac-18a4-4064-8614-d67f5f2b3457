[{"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\AnimatedSensor\\AnimatedSensorModule.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\AnimatedSensor\\AnimatedSensorModule.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\AnimatedSensor\\AnimatedSensorModule.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Fabric\\PropsRegistry.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Fabric\\PropsRegistry.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Fabric\\PropsRegistry.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Fabric\\ReanimatedCommitHook.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Fabric\\ReanimatedCommitHook.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Fabric\\ReanimatedCommitHook.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Fabric\\ReanimatedCommitMarker.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Fabric\\ReanimatedCommitMarker.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Fabric\\ReanimatedCommitMarker.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Fabric\\ReanimatedMountHook.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Fabric\\ReanimatedMountHook.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Fabric\\ReanimatedMountHook.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Fabric\\ShadowTreeCloner.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Fabric\\ShadowTreeCloner.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Fabric\\ShadowTreeCloner.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\5a46067d0bed641e4005a8b119a656e1\\cpp\\LayoutAnimations\\LayoutAnimationsManager.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\LayoutAnimations\\LayoutAnimationsManager.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\LayoutAnimations\\LayoutAnimationsManager.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\5a46067d0bed641e4005a8b119a656e1\\cpp\\LayoutAnimations\\LayoutAnimationsProxy.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\LayoutAnimations\\LayoutAnimationsProxy.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\LayoutAnimations\\LayoutAnimationsProxy.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\5a46067d0bed641e4005a8b119a656e1\\cpp\\LayoutAnimations\\LayoutAnimationsUtils.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\LayoutAnimations\\LayoutAnimationsUtils.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\LayoutAnimations\\LayoutAnimationsUtils.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\NativeModules\\NativeReanimatedModule.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\NativeModules\\NativeReanimatedModule.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\NativeModules\\NativeReanimatedModule.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\5a46067d0bed641e4005a8b119a656e1\\cpp\\NativeModules\\NativeReanimatedModuleSpec.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\NativeModules\\NativeReanimatedModuleSpec.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\NativeModules\\NativeReanimatedModuleSpec.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\ReanimatedRuntime\\RNRuntimeDecorator.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\ReanimatedRuntime\\RNRuntimeDecorator.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\ReanimatedRuntime\\RNRuntimeDecorator.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\5a46067d0bed641e4005a8b119a656e1\\cpp\\ReanimatedRuntime\\ReanimatedHermesRuntime.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\ReanimatedRuntime\\ReanimatedHermesRuntime.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\ReanimatedRuntime\\ReanimatedHermesRuntime.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\ReanimatedRuntime\\ReanimatedRuntime.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\ReanimatedRuntime\\ReanimatedRuntime.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\ReanimatedRuntime\\ReanimatedRuntime.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\ReanimatedRuntime\\WorkletRuntime.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\ReanimatedRuntime\\WorkletRuntime.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\ReanimatedRuntime\\WorkletRuntime.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\5a46067d0bed641e4005a8b119a656e1\\cpp\\ReanimatedRuntime\\WorkletRuntimeDecorator.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\ReanimatedRuntime\\WorkletRuntimeDecorator.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\ReanimatedRuntime\\WorkletRuntimeDecorator.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\5a46067d0bed641e4005a8b119a656e1\\cpp\\ReanimatedRuntime\\WorkletRuntimeRegistry.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\ReanimatedRuntime\\WorkletRuntimeRegistry.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\ReanimatedRuntime\\WorkletRuntimeRegistry.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Registries\\EventHandlerRegistry.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Registries\\EventHandlerRegistry.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Registries\\EventHandlerRegistry.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\SharedItems\\Shareables.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\SharedItems\\Shareables.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\SharedItems\\Shareables.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Tools\\AsyncQueue.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\AsyncQueue.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\AsyncQueue.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Tools\\FeaturesConfig.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\FeaturesConfig.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\FeaturesConfig.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Tools\\JSISerializer.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\JSISerializer.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\JSISerializer.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Tools\\JSLogger.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\JSLogger.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\JSLogger.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Tools\\JSScheduler.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\JSScheduler.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\JSScheduler.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Tools\\ReanimatedJSIUtils.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\ReanimatedJSIUtils.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\ReanimatedJSIUtils.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Tools\\ReanimatedVersion.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\ReanimatedVersion.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\ReanimatedVersion.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Tools\\UIRuntimeDecorator.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\UIRuntimeDecorator.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\UIRuntimeDecorator.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Tools\\UIScheduler.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\UIScheduler.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\UIScheduler.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\6576f2db8d34274020ed0378874df611\\Common\\cpp\\Tools\\WorkletEventHandler.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\WorkletEventHandler.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\Common\\cpp\\Tools\\WorkletEventHandler.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\src\\main\\cpp\\AndroidLogger.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\AndroidLogger.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\AndroidLogger.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\src\\main\\cpp\\AndroidUIScheduler.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\AndroidUIScheduler.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\AndroidUIScheduler.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\src\\main\\cpp\\JNIHelper.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\JNIHelper.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\JNIHelper.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\src\\main\\cpp\\LayoutAnimations.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\LayoutAnimations.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\LayoutAnimations.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\src\\main\\cpp\\NativeProxy.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\NativeProxy.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\NativeProxy.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\src\\main\\cpp\\OnLoad.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\OnLoad.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\OnLoad.cpp"}, {"directory": "H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/.cxx/Debug/2v5m6h18/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android26 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/AnimatedSensor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Fabric\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/hidden_headers\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/LayoutAnimations\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/NativeModules\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/ReanimatedRuntime\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Registries\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/SharedItems\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/../Common/cpp/Tools\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/callinvoker\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/yoga\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/componentregistry\" -I\"H:/project App 2025/codecanyon-CarSport/main-files/carspot/carspot/node_modules/react-native/ReactCommon/react/renderer/core\" -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/folly_runtime/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/glog/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/reactnativejni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/96f49ffbed5d5e97cf1c70574efe45b1/transformed/jetified-fbjni-0.5.1/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/34ed84f9f7d41443bb4242a6896db335/transformed/jetified-hermes-android-0.73.8-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.8/transforms/24bc47c525967d5c2a6bcc66e53ba8c8/transformed/jetified-react-android-0.73.8-debug/prefab/modules/hermes_executor/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=73 -DREANIMATED_VERSION=3.13.0 -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all -std=c++20 -Wall -Wpedantic -Werror -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o CMakeFiles\\reanimated.dir\\src\\main\\cpp\\TurboModule.cpp.o -c \"H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\TurboModule.cpp\"", "file": "H:\\project App 2025\\codecanyon-CarSport\\main-files\\carspot\\carspot\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\TurboModule.cpp"}]